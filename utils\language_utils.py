"""
Language utilities for the Voice Agents Platform.

This module provides language detection, gender detection, dialect handling,
and translation helpers with integrated structured logging.
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import re
import unicodedata

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode

# Module logger
logger = get_module_logger("language_utils")


class LanguageDetector:
    """Language detection utilities with logging."""
    
    def __init__(self):
        self.supported_languages = {
            'en': 'English',
            'es': 'Spanish', 
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic',
            'hi': 'Hindi'
        }
        
        # Simple language detection patterns (in production, use proper language detection library)
        self.language_patterns = {
            'en': [r'\b(the|and|or|but|in|on|at|to|for|of|with|by)\b'],
            'es': [r'\b(el|la|los|las|y|o|pero|en|con|de|para|por)\b'],
            'fr': [r'\b(le|la|les|et|ou|mais|dans|avec|de|pour|par)\b'],
            'de': [r'\b(der|die|das|und|oder|aber|in|mit|von|für|durch)\b'],
            'it': [r'\b(il|la|i|le|e|o|ma|in|con|di|per|da)\b'],
            'pt': [r'\b(o|a|os|as|e|ou|mas|em|com|de|para|por)\b'],
        }
        
        logger.info(
            "Initialized LanguageDetector",
            action="initialize",
            output_data={"supported_languages": len(self.supported_languages)},
            layer="language_utils"
        )
    
    def detect_language(self, text: str, session_id: str = None) -> StateOutput:
        """
        Detect the language of input text.
        
        Args:
            text: Input text to analyze
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with detected language information
        """
        try:
            logger.info(
                "Starting language detection",
                action="detect_language",
                input_data={"text_length": len(text), "text_preview": text[:50]},
                layer="language_utils",
                session_id=session_id
            )
            
            if not text or not text.strip():
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty text provided for language detection",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_text"}
                )
            
            text_lower = text.lower()
            language_scores = {}
            
            # Score each language based on pattern matches
            for lang_code, patterns in self.language_patterns.items():
                score = 0
                for pattern in patterns:
                    matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
                    score += matches
                language_scores[lang_code] = score
            
            # Find the language with highest score
            detected_lang = max(language_scores, key=language_scores.get) if language_scores else 'en'
            confidence = language_scores.get(detected_lang, 0) / len(text.split()) if text.split() else 0
            confidence = min(confidence, 1.0)  # Cap at 1.0
            
            # If confidence is too low, default to English
            if confidence < 0.1:
                detected_lang = 'en'
                confidence = 0.5
            
            result = {
                "language": detected_lang,
                "language_name": self.supported_languages.get(detected_lang, "Unknown"),
                "confidence": confidence,
                "scores": language_scores
            }
            
            logger.info(
                "Language detection completed",
                action="detect_language",
                output_data=result,
                layer="language_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Detected language: {result['language_name']}",
                code=StatusCode.OK,
                outputs=result,
                meta={"confidence": confidence}
            )
            
        except Exception as e:
            logger.error(
                "Error in language detection",
                action="detect_language",
                input_data={"text_length": len(text) if text else 0},
                reason=str(e),
                layer="language_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Language detection failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


class GenderDetector:
    """Gender detection from voice/text patterns with logging."""
    
    def __init__(self):
        # Simple gender indicators (in production, use proper voice analysis)
        self.gender_indicators = {
            'male': ['mr', 'sir', 'gentleman', 'guy', 'man', 'boy', 'father', 'dad', 'husband'],
            'female': ['ms', 'mrs', 'miss', 'madam', 'lady', 'woman', 'girl', 'mother', 'mom', 'wife'],
            'neutral': ['person', 'individual', 'they', 'them', 'their']
        }
        
        logger.info(
            "Initialized GenderDetector",
            action="initialize",
            layer="language_utils"
        )
    
    def detect_gender(self, text: str, voice_features: Dict[str, Any] = None, session_id: str = None) -> StateOutput:
        """
        Detect gender from text and optional voice features.
        
        Args:
            text: Input text to analyze
            voice_features: Optional voice analysis features
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with gender detection results
        """
        try:
            logger.info(
                "Starting gender detection",
                action="detect_gender",
                input_data={
                    "text_length": len(text) if text else 0,
                    "has_voice_features": voice_features is not None
                },
                layer="language_utils",
                session_id=session_id
            )
            
            gender_scores = {'male': 0, 'female': 0, 'neutral': 0}
            
            if text:
                text_lower = text.lower()
                for gender, indicators in self.gender_indicators.items():
                    for indicator in indicators:
                        if indicator in text_lower:
                            gender_scores[gender] += 1
            
            # Analyze voice features if provided
            if voice_features:
                pitch = voice_features.get('pitch', 0)
                if pitch > 200:  # Simplified: higher pitch suggests female
                    gender_scores['female'] += 2
                elif pitch < 150:  # Lower pitch suggests male
                    gender_scores['male'] += 2
                else:
                    gender_scores['neutral'] += 1
            
            # Determine most likely gender
            detected_gender = max(gender_scores, key=gender_scores.get)
            confidence = gender_scores[detected_gender] / max(sum(gender_scores.values()), 1)
            
            # Default to neutral if confidence is low
            if confidence < 0.3:
                detected_gender = 'neutral'
                confidence = 0.5
            
            result = {
                "gender": detected_gender,
                "confidence": confidence,
                "scores": gender_scores
            }
            
            logger.info(
                "Gender detection completed",
                action="detect_gender",
                output_data=result,
                layer="language_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message=f"Detected gender: {detected_gender}",
                code=StatusCode.OK,
                outputs=result,
                meta={"confidence": confidence}
            )
            
        except Exception as e:
            logger.error(
                "Error in gender detection",
                action="detect_gender",
                reason=str(e),
                layer="language_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Gender detection failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


class TextNormalizer:
    """Text normalization and cleaning utilities."""
    
    def __init__(self):
        logger.info(
            "Initialized TextNormalizer",
            action="initialize",
            layer="language_utils"
        )
    
    def normalize_text(self, text: str, language: str = 'en', session_id: str = None) -> StateOutput:
        """
        Normalize and clean input text.
        
        Args:
            text: Input text to normalize
            language: Language code for language-specific normalization
            session_id: Optional session ID for context
            
        Returns:
            StateOutput with normalized text
        """
        try:
            logger.info(
                "Starting text normalization",
                action="normalize_text",
                input_data={
                    "text_length": len(text) if text else 0,
                    "language": language
                },
                layer="language_utils",
                session_id=session_id
            )
            
            if not text:
                return StateOutput(
                    status=StatusType.ERROR,
                    message="Empty text provided for normalization",
                    code=StatusCode.BAD_REQUEST,
                    outputs={},
                    meta={"error": "empty_text"}
                )
            
            # Basic normalization steps
            normalized = text.strip()
            
            # Unicode normalization
            normalized = unicodedata.normalize('NFKC', normalized)
            
            # Remove extra whitespace
            normalized = re.sub(r'\s+', ' ', normalized)
            
            # Language-specific normalization
            if language == 'en':
                # English-specific normalizations
                normalized = re.sub(r"won't", "will not", normalized)
                normalized = re.sub(r"can't", "cannot", normalized)
                normalized = re.sub(r"n't", " not", normalized)
                normalized = re.sub(r"'re", " are", normalized)
                normalized = re.sub(r"'ve", " have", normalized)
                normalized = re.sub(r"'ll", " will", normalized)
                normalized = re.sub(r"'d", " would", normalized)
            
            result = {
                "original_text": text,
                "normalized_text": normalized,
                "language": language,
                "changes_made": text != normalized
            }
            
            logger.info(
                "Text normalization completed",
                action="normalize_text",
                output_data={
                    "original_length": len(text),
                    "normalized_length": len(normalized),
                    "changes_made": result["changes_made"]
                },
                layer="language_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.SUCCESS,
                message="Text normalized successfully",
                code=StatusCode.OK,
                outputs=result,
                meta={"language": language}
            )
            
        except Exception as e:
            logger.error(
                "Error in text normalization",
                action="normalize_text",
                input_data={"text_length": len(text) if text else 0},
                reason=str(e),
                layer="language_utils",
                session_id=session_id
            )
            
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Text normalization failed: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={},
                meta={"error": str(e)}
            )


# Convenience functions for easy access
def detect_language(text: str, session_id: str = None) -> StateOutput:
    """Convenience function for language detection."""
    detector = LanguageDetector()
    return detector.detect_language(text, session_id)


def detect_gender(text: str, voice_features: Dict[str, Any] = None, session_id: str = None) -> StateOutput:
    """Convenience function for gender detection."""
    detector = GenderDetector()
    return detector.detect_gender(text, voice_features, session_id)


def normalize_text(text: str, language: str = 'en', session_id: str = None) -> StateOutput:
    """Convenience function for text normalization."""
    normalizer = TextNormalizer()
    return normalizer.normalize_text(text, language, session_id)
