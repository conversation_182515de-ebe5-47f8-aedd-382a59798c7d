import asyncio
from agents.Layer2.preprocessing_agent import PreprocessingAgent
from dotenv import load_dotenv
load_dotenv()

async def main():
    session_id = "test_session"
    redis_key = session_id
    # Simulate saving transcript to shared context in Redis
    agent = PreprocessingAgent(session_id=session_id, state_id="test_state")
    test_transcript = "   This   is   a   TEST   text!   "
    await agent.save_context(redis_key, {"transcript": test_transcript})
    # Now run the agent to preprocess
    print(f"Preprocessing transcript from Redis key: {redis_key}...")
    result = await agent.process({})
    print("Result:")
    print(result.model_dump())
    print(await agent.load_context(redis_key),"Context after Preprocessing Agent ")

if __name__ == "__main__":
    asyncio.run(main()) 