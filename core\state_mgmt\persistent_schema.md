# Persistent Memory Layer Schema

This document describes the MongoDB schema for the Persistent Memory Layer, which provides long-term storage for user, session, pipeline, and intent history data. This layer enables cross-session personalization, analytics, and operational insights for the AI Voice Agents Platform.

---

## 🧑 User Collection (`users`)

**Purpose:** Store user profiles, preferences, and engagement metrics for long-term personalization and analytics.

| Field                | Type            | Description                                      | Index |
|----------------------|-----------------|--------------------------------------------------|-------|
| `user_id`            | `str`           | Unique user identifier (UUID or platform ID)      | ✅    |
| `created_at`         | `datetime`      | Account creation timestamp                        |       |
| `last_seen`          | `datetime`      | Last interaction timestamp                        |       |
| `call_count`         | `int`           | Number of calls/sessions with the platform        |       |
| `tags`               | `List[str]`     | User tags (e.g., ["VIP", "slow_speaker"])      |       |
| `voice_profile`      | `dict`          | {`emotion_pref`, `gender_pref`, `voice_id`}       |       |
| `preferred_language` | `str`           | User's preferred language (e.g., "en", "ar")    |       |
| `default_intent`     | `str`           | Default/frequent intent (e.g., "check_balance")  |       |
| `feedback_score_avg` | `float`         | Average feedback score from user                  |       |

**Indexes:**
- `user_id` (unique)

---

## 📞 Call Session Collection (`call_sessions`)

**Purpose:** Log each call/session with detailed metadata for analytics, troubleshooting, and personalization.

| Field                  | Type            | Description                                         | Index |
|------------------------|-----------------|-----------------------------------------------------|-------|
| `session_id`           | `str`           | Unique session/call identifier                      | ✅    |
| `user_id`              | `str`           | User identifier (foreign key to `users`)            | ✅    |
| `timestamp`            | `datetime`      | Session start time                                  |       |
| `agent_profile_used`   | `str`           | Agent profile or persona used                       |       |
| `pipeline_id`          | `str`           | Pipeline executed in this session                   | ✅    |
| `states_visited`       | `List[str]`     | List of state IDs visited during the session        |       |
| `interrupts`           | `List[str]`     | List of interrupts (e.g., "user_hangup")           |       |
| `outcome`              | `str`           | { success | failure | referred }                    |       |
| `final_action`         | `str`           | Last action taken in the session                    |       |
| `call_score`           | `int`           | User/agent score for the call [0-10]                |       |
| `duration_sec`         | `float`         | Call/session duration in seconds                    |       |
| `latency_avg_ms`       | `float`         | Average latency per turn (ms)                       |       |
| `tts_characters_used`  | `int`           | Number of TTS characters used in the session        |       |

**Indexes:**
- `session_id` (unique)
- `user_id`
- `pipeline_id`

---

## 🔄 Pipelines Collection (`pipelines`)

**Purpose:** Track pipeline definitions, user progress, and metadata for analytics and versioning.

| Field            | Type            | Description                                 | Index |
|------------------|-----------------|---------------------------------------------|-------|
| `pipeline_id`    | `str`           | Unique pipeline identifier                  | ✅    |
| `user_id`        | `str`           | User identifier (if user-specific)          | ✅    |
| `workflow_states`| `List[str]`     | List of workflow state IDs                  |       |
| `metadata`       | `dict`          | {title, vertical, description}              |       |
| `version`        | `str`           | Pipeline version (e.g., "v1.0")            |       |
| `created_by`     | `str`           | Creator (user or system)                    |       |

**Indexes:**
- `pipeline_id` (unique)
- `user_id`

---

## 💬 Intent History Collection (`intent_history`)

**Purpose:** Store detected intents, raw inputs, and context for each user/session turn for analytics and personalization.

| Field             | Type            | Description                                 | Index |
|-------------------|-----------------|---------------------------------------------|-------|
| `intent_id`       | `str`           | Unique intent record identifier             | ✅    |
| `user_id`         | `str`           | User identifier                             | ✅    |
| `session_id`      | `str`           | Session identifier                          | ✅    |
| `raw_input`       | `str`           | Raw user input                              |       |
| `intent_detected` | `str`           | Detected intent                             |       |
| `state_id`        | `str`           | State in which intent was detected          |       |
| `success`         | `bool`          | Whether the intent was successfully handled |       |
| `context_snapshot`| `dict`          | Context at the time of intent detection     |       |

**Indexes:**
- `intent_id` (unique)
- `user_id`
- `session_id`

---

## General Notes
- All collections should support schema versioning via an optional `schema_version` field.
- Use MongoDB indexes for all primary and foreign keys for efficient querying.
- Collections are designed to be modular and extensible for future needs.
- See `PersistentMemory` interface for programmatic access patterns. 