import asyncio
import uuid
import os
from agents.orchestrator_agent import Orchestrator
from agents.Layer2.stt_agent import STTAgent
from agents.Layer2.preprocessing_agent import PreprocessingAgent
from agents.Layer2.processing_agent import ProcessingAgent
from agents.Layer2.tts_agent import TTSAgent
from utils.redis_client import RedisClient
from schemas.a2a_message import A2AMessage, MessageType
from core.state_mgmt.memory_manager import MemoryManager

# Path to your audio files
AUDIO_FILES = [
    "fillerWords/user_conversation_part_1.mp3",
    "fillerWords/user_conversation_part_2.mp3",
    "fillerWords/user_conversation_part_3.mp3",
]

print("[TEST] Starting full system E2E test script...")

async def agent_runner(agent_class, agent_name, session_id):
    redis = RedisClient()
    instruction_channel = f"agent:{agent_name}:{session_id}"
    print(f"[{agent_name}] Listening on '{instruction_channel}'")

    async def callback(message_json):
        message = A2AMessage.from_json(message_json)
        print(f"[{agent_name}] Received instruction: {message.payload}")
        agent = agent_class(session_id=session_id, state_id="test_state")
        # For STT, process the audio file; for others, just pass {}
        if agent_name == "stt_agent":
            audio_path = message.payload.get("audio_path")
            with open(audio_path, "rb") as f:
                audio_bytes = f.read()
            result = await agent.process(audio_bytes, session_context={"language": "en", "response_format": "text"})
        else:
            result = await agent.process({})
        print(f"[{agent_name} Output]:", result.model_dump())
        # Do not exit; keep listening for more instructions

    try:
        await redis.subscribe(instruction_channel, callback)
    except asyncio.CancelledError:
        print(f"[{agent_name}] Task complete, shutting down listener.")
    finally:
        await redis.close()

async def run_e2e_test(audio_file):
    session_id = f"test_session_{uuid.uuid4().hex[:8]}"
    print(f"\n=== Starting E2E Test for session: {session_id} with audio: {audio_file} ===")

    # Start orchestrator as a background task
    orchestrator = Orchestrator(workflow_name="GenericBank.json")
    orch_task = asyncio.create_task(orchestrator.start())
    await asyncio.sleep(1)  # Give orchestrator time to start

    # Start all agents as background tasks
    stt_task = asyncio.create_task(agent_runner(STTAgent, "stt_agent", session_id))
    pre_task = asyncio.create_task(agent_runner(PreprocessingAgent, "preprocessing_agent", session_id))
    proc_task = asyncio.create_task(agent_runner(ProcessingAgent, "processing_agent", session_id))
    tts_task = asyncio.create_task(agent_runner(TTSAgent, "tts_agent", session_id))

    await asyncio.sleep(1)  # Give agents time to subscribe

    # Send initial instruction to STT agent to start the workflow
    redis = RedisClient()
    first_instruction = A2AMessage(
        session_id=session_id,
        message_type=MessageType.INSTRUCTION,
        source_agent="System",
        target_agent="stt_agent",
        payload={"action": "process_audio", "audio_path": audio_file}
    )
    await redis.publish(f"agent:stt_agent:{session_id}", first_instruction.to_json())

    # Wait for orchestrator and agents to finish (wait for TTS agent to process, then cancel all tasks)
    # For this test, we'll wait a reasonable time, then cancel
    try:
        await asyncio.sleep(15)  # Wait for the workflow to complete (adjust as needed)
    finally:
        for task in [stt_task, pre_task, proc_task, tts_task, orch_task]:
            task.cancel()
        await asyncio.gather(stt_task, pre_task, proc_task, tts_task, orch_task, return_exceptions=True)

    # Print final context and conversation
    memory = MemoryManager(session_id)
    print("\n=== Final Contextual Memory ===")
    print(await memory.get_all_contextual())
    print("\n=== Conversation History ===")
    print(await memory.get_conversation())
    print("\n[TEST] Finished E2E test for session:", session_id)
    print("\n" + "="*60 + "\n")

if __name__ == "__main__":
    # Run the test for each audio file in AUDIO_FILES
    for audio_file in AUDIO_FILES:
        if not os.path.exists(audio_file):
            print(f"Audio file not found: {audio_file}")
            continue
        asyncio.run(run_e2e_test(audio_file)) 