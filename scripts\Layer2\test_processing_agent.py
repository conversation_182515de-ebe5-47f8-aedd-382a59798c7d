import asyncio
from agents.Layer2.processing_agent import ProcessingAgent
from dotenv import load_dotenv
load_dotenv()

async def main():
    session_id = "test_session_processing"
    redis_key = session_id
    # Simulate saving clean_text to shared context in Redis
    agent = ProcessingAgent(session_id=session_id, state_id="test_state")
    test_clean_text = "please show my account balance from the database using knowledge base"
    await agent.save_context(redis_key, {"clean_text": test_clean_text})
    # Now run the agent to process
    print(f"Processing clean_text from Redis key: {redis_key}...")
    result = await agent.process({})
    print("Result:")
    print(result.model_dump())
    print(await agent.load_context(redis_key),"Context after Processing Agent ")

if __name__ == "__main__":
    asyncio.run(main()) 