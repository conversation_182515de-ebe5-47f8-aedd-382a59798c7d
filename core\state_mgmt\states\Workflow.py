from typing import List, Dict, Optional, Union
from pydantic import BaseModel
from asteval import Interpreter

from .transition import Transition  



class State(BaseModel):
    id: str
    type: str
    layer2_id: str
    expected_input: List[str]
    expected_output: List[str]
    transitions: Optional[List[Transition]] = []
    allowed_tools: List[str]


class WorkflowConfig(BaseModel):
    id: str
    name: str
    version: str
    start: str
    allowed_actions:List[str]
    prohibited_actions:List[str]
    states: Dict[str, State]


class WorkflowWrapper(BaseModel):
    workflow: WorkflowConfig
