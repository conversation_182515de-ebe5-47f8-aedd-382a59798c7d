from pathlib import Path
import sys
import os

project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

import unittest
from unittest.mock import patch, MagicMock, AsyncMock
import json
import sys
import asyncio

from utils.mongo_dialog_saver import save_dialog_to_mongo

class TestMongoDialogSaver(unittest.IsolatedAsyncioTestCase):
    @patch('utils.mongo_client.AsyncIOMotorClient')
    async def test_save_dialog_to_mongo(self, mock_motor_client):
        # Set up the mock client, db, and collection
        mock_client = MagicMock()
        mock_db = MagicMock()
        mock_collection = MagicMock()
        # Make create_index awaitable
        mock_collection.create_index = AsyncMock()
        # Make insert_one awaitable
        mock_insert_one = AsyncMock()
        mock_insert_one.return_value.inserted_id = 'fake_id123'
        mock_collection.insert_one = mock_insert_one
        mock_db.__getitem__.return_value = mock_collection
        mock_client.__getitem__.return_value = mock_db
        mock_motor_client.return_value = mock_client

        inserted_id = await save_dialog_to_mongo('sess1', 'user1', 123, 8, 'Good agent')
        self.assertEqual(inserted_id, 'fake_id123')
        mock_insert_one.assert_awaited_once()
        # Ensure create_index was awaited three times (for each index)
        self.assertEqual(mock_collection.create_index.await_count, 3)

class TestLLMOutputParsing(unittest.TestCase):
    def test_llm_output_json_parsing(self):
        llm_output = '{"summary": "Test summary", "learnings": ["fact1", "fact2"], "score": 9, "justification": "Great job"}'
        try:
            data = json.loads(llm_output)
        except Exception:
            data = None
        self.assertIsInstance(data, dict)
        self.assertIn('summary', data)
        self.assertIn('learnings', data)
        self.assertIn('score', data)
        self.assertIn('justification', data)

if __name__ == '__main__':
    unittest.main() 