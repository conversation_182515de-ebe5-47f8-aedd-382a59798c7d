import asyncio
import os
from agents.Layer2.tts_agent_openAI import TTSAgentOpenAI
from dotenv import load_dotenv

load_dotenv()

async def main():
    session_id = "test_session_tts"
    state_id = "test_state"
    agent = TTSAgentOpenAI(session_id=session_id, state_id=state_id)
    # Simulate context with emotion and gender
    test_text = "Hello, this is <PERSON><PERSON>, welcome!"
    context = {
        "emotion": "happy",
        "gender": "female",
        "llm_answer": test_text
    }
    # Save context to Redis
    await agent.save_context(session_id, context)
    # Run the agent
    print("Running TTSAgent...")
    result = await agent.process({})
    print("[TTSAgent Output]:", result.model_dump())
    # Print audio path
    audio_path = result.outputs.get("audio_path")
    print("[Audio Path]:", audio_path)
    # Play the audio if possible
    try:
        from playsound import playsound
        print("Playing audio...")
        playsound(audio_path)
    except ImportError:
        print("playsound not installed. Please play the audio file manually:", audio_path)
    except Exception as e:
        print(f"Could not play audio: {e}\nPlease play the file manually: {audio_path}")
    # Print updated context
    updated_context = await agent.load_context(session_id)
    print("[Updated Context]:", updated_context)

if __name__ == "__main__":
    asyncio.run(main()) 