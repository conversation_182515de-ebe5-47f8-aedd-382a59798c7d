{"id": "l2_check_balance", "version": "1.0", "pipeline": [{"step": "PreProcessing", "agent": "validate_account_id", "input": {"account_id": "{{slots.account_id}}"}, "tools": {"memory": "mem_persi_1", "external_tools": "openai"}, "output": {"validated_account_id": "account_id"}}, {"step": "Processing", "agent": "balance_fetcher", "input": {"account_id": "account_id"}, "tools": {"memory": "mem_persi_1", "external_tools": "balance_checker"}, "output": {"balance": "account_balance"}}, {"step": "ResponseFormulation", "agent": "llm_response_gen", "input": {"balance": "account_balance"}, "tools": {"memory": "mem_persi_1", "external_tools": "openai"}, "output": {"text": "response_text"}}, {"step": "TTS", "agent": "tts_google", "input": {"text": "response_text"}, "tools": {"memory": "mem_persi_1", "external_tools": "openai"}, "output": {"audio": "audio_output"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "Processing"}, "onError": {"retry": 1, "fallback_state": "l2_fallback_generic"}}