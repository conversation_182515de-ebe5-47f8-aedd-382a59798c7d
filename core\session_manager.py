import logging
from datetime import datetime
from typing import Optional

from core.logger_config import get_module_logger

logger = get_module_logger("session_manager")

class SessionManager:
    """
    Manages session lifecycle: cleanup, persistent memory saving, and metadata updates.
    Integrates orchestrator, StateManager, and MemoryManager logic.
    """
    def __init__(self, redis_client, get_state_manager_by_session_id, orchestrator_sessions=None, orchestrator_metadata=None, orchestrator_pipeline_progress=None):
        """
        Args:
            redis_client: Redis client instance (async)
            get_state_manager_by_session_id: Callable to get StateManager by session_id
            orchestrator_sessions: Optional dict for orchestrator's session registry
            orchestrator_metadata: Optional dict for orchestrator's session metadata
            orchestrator_pipeline_progress: Optional dict for orchestrator's pipeline progress
        """
        self.redis = redis_client
        self.get_state_manager = get_state_manager_by_session_id
        self.sessions = orchestrator_sessions if orchestrator_sessions is not None else {}
        self.session_metadata = orchestrator_metadata if orchestrator_metadata is not None else {}
        self.pipeline_progress = orchestrator_pipeline_progress if orchestrator_pipeline_progress is not None else {}
        self.logger = logger

    async def cleanup_session(self, session_id: str, agent_name: str, reason: str):
        """
        Clean up session data in orchestrator and Redis.
        Mirrors orchestrator_agent_v2.py _cleanup_session logic.
        """
        try:
            state_manager = self.get_state_manager(session_id)

            # Use scan instead of keys to avoid blocking Redis
            try:
                keys = []
                cursor = 0
                while True:
                    cursor, batch = await self.redis.client.scan(cursor, match=f"session:{session_id}:*", count=100)
                    keys.extend(batch)
                    if cursor == 0:
                        break
                if keys:
                    await self.redis.delete(*keys)
            except Exception as e:
                self.logger.warning(
                    f"Could not clean up Redis keys for session {session_id}: {e}",
                    action="cleanup_session",
                    input_data={"session_id": session_id},
                    layer="session_manager",
                    step="cleanup_redis_keys"
                )
            self.sessions.pop(session_id, None)
            self.session_metadata.pop(session_id, None)
            self.pipeline_progress.pop(session_id, None)
            self.logger.info(
                f"Cleaned up session data: {reason}",
                action="cleanup_session",
                input_data={"session_id": session_id, "agent_name": agent_name},
                status="success",
                layer="session_manager",
                step="cleanup_session"
            )
        except Exception as e:
            self.logger.error(
                "Failed to clean up session data",
                action="cleanup_session",
                input_data={"session_id": session_id, "agent_name": agent_name},
                reason=str(e),
                layer="session_manager",
                step="cleanup_session"
            )

    async def end_session_cleanup(self, state_manager):
        """
        Mark session as completed and set session end metadata.
        Mirrors StateManager.end_session_cleanup logic.
        """
        try:
            await state_manager.memory_manager.set("contextual", "outcome", "completed")
            await state_manager.memory_manager.set("contextual", "session_end_time", datetime.now().isoformat())
            final_action = await state_manager.memory_manager.get("final_action")
            if not final_action:
                await state_manager.memory_manager.set("contextual", "final_action", "session_cleanup")
            self.logger.info(
                "Session cleanup completed",
                action="end_session_cleanup",
                layer="session_manager"
            )
        except Exception as e:
            self.logger.error(
                "Error during session cleanup",
                action="end_session_cleanup",
                reason=str(e),
                layer="session_manager"
            )

    async def save_persistent_memory(self, state_manager):
        """
        Save all persistent memory data to MongoDB (dialog, user, call session, intent history).
        Mirrors MemoryManager.save_persistent_memory_data logic.
        """
        try:
            if hasattr(state_manager.memory_manager, 'save_persistent_memory_data'):
                await state_manager.memory_manager.save_persistent_memory_data()
            self.logger.info(
                "Successfully saved persistent memory data",
                action="save_persistent_memory",
                layer="session_manager"
            )
        except Exception as e:
            self.logger.error(
                f"Failed to save persistent memory data: {e}",
                action="save_persistent_memory",
                reason=str(e),
                layer="session_manager"
            )

    async def full_cleanup(self, session_id: str, agent_name: str, reason: str):
        """
        Perform full session cleanup: mark as completed, save persistent memory, and clean up orchestrator/Redis.
        """
        state_manager = self.get_state_manager(session_id)
        if state_manager:
            await self.end_session_cleanup(state_manager)
            await self.save_persistent_memory(state_manager)
        await self.cleanup_session(session_id, agent_name, reason) 