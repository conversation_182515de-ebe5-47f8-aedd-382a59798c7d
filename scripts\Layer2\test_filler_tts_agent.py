import asyncio
from agents.Layer2.filler_tts_agent import FillerTTSAgent
from playsound import playsound

async def test_filler_tts_agent_default():
    print("Test: FillerTTSAgent with random filler text")
    agent = FillerTTSAgent(session_id="test_session_filler")
    result = await agent.process({})
    print("Result:", result.model_dump())
    audio_path = result.outputs.get("audio_path")
    if audio_path:
        try:
            print("Playing audio...")
            playsound(audio_path)
        except ImportError:
            print("playsound not installed. Please play the audio file manually:", audio_path)
        except Exception as e:
            print(f"Could not play audio: {e}\nPlease play the file manually: {audio_path}")

async def test_filler_tts_agent_custom():
    print("Test: FillerTTSAgent with custom filler text")
    agent = FillerTTSAgent(session_id="test_session_filler")
    result = await agent.process({"filler_text": "Please hold on while I check that for you..."})
    print("Result:", result.model_dump())
    audio_path = result.outputs.get("audio_path")
    if audio_path:
        try:
            print("Playing audio...")
            playsound(audio_path)
        except ImportError:
            print("playsound not installed. Please play the audio file manually:", audio_path)
        except Exception as e:
            print(f"Could not play audio: {e}\nPlease play the file manually: {audio_path}")

if __name__ == "__main__":
    asyncio.run(test_filler_tts_agent_default())
    asyncio.run(test_filler_tts_agent_custom()) 