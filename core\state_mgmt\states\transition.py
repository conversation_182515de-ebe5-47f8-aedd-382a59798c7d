from asteval import Interpreter
from pydantic import BaseModel
import sys
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Add project root to path for imports
project_root = str(Path(__file__).parent.parent.parent.parent)
sys.path.append(project_root)

from core.logger_config import get_module_logger

# Module logger for transition evaluation
transition_logger = get_module_logger("transition")

# Add this executor at the module level
_executor = ThreadPoolExecutor(max_workers=5)


class Transition(BaseModel):
    condition: str
    target: str

    def __repr__(self):
        return f"Transition(condition={self.condition}, target={self.target})"
    
    async def evaluate(self, aeval: Interpreter) -> bool:
        """
        Evaluates the transition condition against the provided context.
        
        Args:
            aeval (Interpreter): The Asteval interpreter with context.
        
        Returns:
            bool: True if the condition is met, False otherwise.
        """
        try:
            if self.condition == "true":
                return True
            
            # Run potentially complex evaluations in a thread pool to avoid blocking
            loop = asyncio.get_running_loop()
            shouldAdvance = await loop.run_in_executor(
                _executor, 
                lambda: aeval(self.condition)
            )
            
            return shouldAdvance == True
        except Exception as e:
            transition_logger.error(
                "Error evaluating transition condition",
                action="evaluate",
                input_data={"condition": self.condition},
                reason=str(e),
                layer="transition"
            )
            return False
    

    async def createAeval(self, context: dict) -> Interpreter:
        """
        Creates an Asteval interpreter with the provided context.
        
        Args:
            context (dict): The context to be used in the Asteval interpreter.
        
        Returns:
            Interpreter: An instance of Asteval interpreter with the context.
        """
        aeval = Interpreter()
        for key, value in context.items():
            aeval.symtable[key] = value
        return aeval
