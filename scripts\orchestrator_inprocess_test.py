import asyncio
import uuid
from agents.orchestrator_agent_v2 import OrchestratorV2

# Configuration
WORKFLOW_NAME = "AgentsTry.json"
AUDIO_FILE = "fillerWords/user_conversation_part_2.mp3"

async def test_orchestrator_only():
    """
    Test the orchestrator agent v2 running standalone.
    The orchestrator should:
    1. Get the first state from the workflow
    2. Start executing the first state via state manager
    3. Monitor agent completions until final agent is done
    4. Evaluate and decide whether to proceed or redo
    """
    session_id = f"test_orchestrator_{uuid.uuid4().hex[:8]}"
    
    print(f"=== Testing Orchestrator V2 Standalone: {session_id} ===")
    print(f"Workflow: {WORKFLOW_NAME}")
    print(f"Audio File: {AUDIO_FILE}")
    
    # Create and run orchestrator
    orchestrator = OrchestratorV2(workflow_name=WORKFLOW_NAME)
    
    try:
        # Run the session - orchestrator handles everything
        await orchestrator.run_session(WORKFLOW_NAME, AUDIO_FILE, session_id=session_id)
        
        print(f"\n✅ Orchestrator test completed successfully for session: {session_id}")
        
    except Exception as e:
        print(f"\n❌ Orchestrator test failed: {e}")
        raise
    finally:
        # Clean up
        try:
            await orchestrator.close()
        except Exception as e:
            print(f"Warning: Error during cleanup: {e}")

if __name__ == "__main__":
    asyncio.run(test_orchestrator_only()) 