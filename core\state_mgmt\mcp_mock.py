from typing import Dict, Any
import asyncio

class MockMCPAgent:
    """
    Mock MCP-compliant agent for testing State and Layer2Pipeline.
    """
    def __init__(self, name: str):
        self.name = name
    
    async def process(self, input_data: dict, session_context: dict) -> Dict[str, Any]:
        """
        Simulates processing for an MCP-compliant agent.
        
        Args:
            input_data (dict): Input data for the agent.
            session_context (dict): Session context including memory access.
        
        Returns:
            Dict[str, Any]: Simulated outputs.
        """
        # Simulate some processing time
        await asyncio.sleep(0.01)
        
        # Logic based on agent name from Layer2 configurations
        if self.name == "stt_google":
            # Simulate Speech-to-Text
            audio = input_data.get("audio", "")
            return {"transcribed_text": audio if audio else "Hello, how can I help you?"}
        
        elif self.name == "intent_classifier":
            # Simulate Intent Detection
            text = input_data["intent"]
            if "balance" in text:
                return {"intent": "check_balance", "slots": {"account_id": session_context.get("account_id", "12345")}}
            elif "loan" in text:
                return {"intent": "loan_inquiry", "slots": {}}
            else:
                return {"intent": "unknown", "slots": {}}
        
        elif self.name == "llm_ack_gen":
            # Simulate LLM Acknowledgment
            intent = input_data.get("intent", "")
            return {"ack_text": f"Got it, you want to {intent.replace('_', ' ')}."}
        
        elif self.name == "tts_google":
            # Simulate Text-to-Speech
            text = input_data.get("text", "")
            if "bye" in text:
                return {"audio_output": f"Audio for: {text}", "exit_signal": "exit"}
            return {"audio_output": f"Audio for: {text}", "acknowledgment": "acknowledged", "transition_signal": "continue"}
        
        elif self.name == "validate_account_id":
            # Simulate Account ID Validation
            account_id = input_data.get("account_id", "")
            return {"account_id": account_id if account_id else "invalid"}
        
        elif self.name == "balance_fetcher":
            # Simulate Balance Fetching
            account_id = input_data.get("account_id", session_context.get("account_id", "12345"))
            return {"account_balance": 5000 if account_id != "invalid" else 0}
        
        elif self.name == "llm_response_gen":
            # Simulate LLM Response Generation
            balance = input_data.get("balance", 0)
            return {"response_text": f"Your balance is ${balance}."}
        
        elif self.name == "llm_farewell_gen":
            # Simulate Farewell Message
            return {"farewell_text": "Thank you for using our service. Goodbye!"}
        
        else:
            return {"output": f"Processed by {self.name}"}