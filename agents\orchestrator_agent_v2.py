import asyncio
import json
import os
import signal
import uuid
from typing import Dict, Any, Set
from utils.redis_client import RedisClient
from core.state_mgmt.StateManager import StateManager
from schemas.a2a_message import A2AMessage, MessageType
from core.logger_config import get_module_logger
from pydantic import ValidationError
import difflib
from dotenv import load_dotenv
from core.state_mgmt.memory_manager import MemoryManager
import google.generativeai as genai
import openai

logger = get_module_logger("orchestrator_v2")

load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

class OrchestratorV2:
    """
    Enhanced Orchestrator Agent V2 with proper pipeline tracking.
    
    This orchestrator:
    1. Gets the first state from the workflow
    2. Starts executing the first state via state manager
    3. Monitors agent completions until final agent is done
    4. Evaluates and decides whether to proceed or redo
    """
    
    def __init__(self, redis_url: str = None, workflow_name: str = "default_workflow"):
        self.redis = RedisClient(redis_url)
        self.workflow_name = workflow_name
        self.sessions: Dict[str, StateManager] = {}
        self.session_metadata: Dict[str, Dict] = {}
        self.pipeline_progress: Dict[str, Dict] = {}
        self.running = False
        self._listen_task = None
        self.max_retries = 3
        self.schema_version = "1.0"
        self.logger = logger

    async def initialize(self):
        """Initialize the orchestrator."""
        await self.redis.initialize()
        self.logger.info(
            "Initialized orchestrator",
            action="initialize",
            layer="orchestrator_v2",
            step="initialize"
        )

    async def start(self):
        """Start the orchestrator and begin listening for agent completions."""
        self.running = True
        self._listen_task = asyncio.create_task(
            self.redis.subscribe("agent_completion", self.handle_agent_completion)
        )
        self.logger.info(
            "Started orchestrator",
            action="start",
            layer="orchestrator_v2",
            step="start"
        )

    async def get_openai_decision(self, user_query, agent_responses, agent_confidence, current_state, workflow):
        """Get decision from OpenAI about whether to proceed or redo."""
        try:
            client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            
            # Enhanced logging of inputs
            self.logger.info(
                "OpenAI Decision Inputs",
                action="get_openai_decision",
                input_data={
                    "user_query": user_query, # retrieved directly from contextual memory (preprocessing --> clean text)
                    "agent_responses": agent_responses, # retrieved from agent_outputs (logger) --> retrieve from contextual memeory (processing --> llm answer)
                    "agent_confidence": agent_confidence, # to be retrieved from redis agent publish
                    "current_state": current_state, # pass to open-ai workflow state (layer1) and pipeline state (2) both should be retrieved from state manager
                    "workflow_name": workflow.get('name', 'Unknown') if workflow else 'Unknown' # passed when starting orchestrator
                },
                layer="orchestrator_v2",
                step="decision_inputs"
            )
            
            # Build the prompt for decision making
            prompt = f"""
            You are an AI orchestrator evaluating a conversation state. Based on the following information, decide whether to PROCEED to the next state or REDO the current state.

            Current State: {current_state}
            User Query: "{user_query}"
            Agent Responses: {agent_responses}
            Agent Confidence: {agent_confidence}

            Workflow Context:
            - Workflow Name: {workflow.get('name', 'Unknown') if workflow else 'Unknown'}
            - Allowed Actions: {workflow.get('allowed_actions', []) if workflow else []}
            - Prohibited Actions: {workflow.get('prohibited_actions', []) if workflow else []}

            Evaluation Criteria:
            1. Did the agents successfully process the user's request? (Check if responses are meaningful)
            2. Are all expected outputs present and valid? (Check if responses contain actual content)
            3. Is the conversation flow logical and complete? (Check if the response addresses the user's query)
            4. Are there any errors or missing information? (Check for empty or error responses)

            IMPORTANT: 
            - If agent_responses is empty or contains only generic messages like "Audio response generated", this indicates missing content
            - If the user query is not empty but agent_responses is empty, this suggests the pipeline failed to process the request
            - Only proceed if there are meaningful responses that address the user's query

            Respond with ONLY one word: "proceed" or "redo"
            """

            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=10,
                temperature=0.1
            )
            
            decision = response.choices[0].message.content.strip().lower()
            final_decision = "proceed" if "proceed" in decision else "redo"
            
            # Enhanced logging of decision
            self.logger.info(
                f"OpenAI Decision: {final_decision}",
                action="get_openai_decision",
                input_data={
                    "user_query": user_query,
                    "agent_responses": agent_responses,
                    "agent_confidence": agent_confidence,
                    "current_state": current_state
                },
                output_data={
                    "raw_response": response.choices[0].message.content,
                    "final_decision": final_decision,
                    "decision_reason": self._get_decision_reason(user_query, agent_responses, final_decision)
                },
                layer="orchestrator_v2",
                step="decision_output"
            )
            
            return final_decision
            
        except Exception as e:
            self.logger.error(
                f"Error getting OpenAI decision: {e}",
                action="get_openai_decision",
                reason=str(e),
                layer="orchestrator_v2",
                step="get_openai_decision"
            )
            return "redo"  # Default to redo on error

    def _get_decision_reason(self, user_query, agent_responses, decision):
        """Get a human-readable reason for the decision."""
        if decision == "redo":
            if not agent_responses:
                return "No agent responses received"
            elif len(agent_responses) == 1 and "Audio response generated" in agent_responses:
                return "Only audio response generated, missing meaningful content"
            elif not user_query:
                return "No user query to evaluate"
            else:
                return "Agent responses insufficient or incomplete"
        else:
            return "Sufficient agent responses received"

    def is_prohibited(self, user_query, prohibited_actions): # to be retrieved from state manager
        """Check if user query contains prohibited actions."""
        if not prohibited_actions:
            return False, None
        
        user_query_lower = user_query.lower()
        for action in prohibited_actions:
            if action.lower() in user_query_lower:
                return True, action
        return False, None

    async def _get_pipeline_agents(self, session_id: str) -> Set[str]: # to be removed
        """Get the expected agents for the current state's Layer2 pipeline."""
        try:
            state_manager = await self._get_or_create_state_manager(session_id)
            current_state = state_manager.get_state(state_manager.current_state_id)
            if not current_state or not current_state.layer2_id:
                return set()
            
            # Load Layer2 configuration to get pipeline agents
            layer2_file = f"config/{current_state.layer2_id}.json"
            if os.path.exists(layer2_file):
                with open(layer2_file, 'r') as f:
                    layer2_config = json.load(f)
                    pipeline_agents = {step["agent"] for step in layer2_config.get("pipeline", [])}
                    return pipeline_agents
            return set()
        except Exception as e:
            self.logger.error(
                "Failed to get pipeline agents",
                action="_get_pipeline_agents",
                input_data={"session_id": session_id},
                reason=str(e),
                layer="orchestrator_v2",
                step="get_pipeline_agents"
            )
            return set()

    async def _is_pipeline_complete(self, session_id: str) -> bool: # to be removed
        """Check if the current state's Layer2 pipeline is complete."""
        if session_id not in self.pipeline_progress:
            return False
        
        progress = self.pipeline_progress[session_id]
        expected_agents = progress.get("expected_agents", set())
        completed_agents = progress.get("completed_agents", set())
        
        return expected_agents.issubset(completed_agents)

    async def _initialize_pipeline_tracking(self, session_id: str): # to be removed
        """Initialize pipeline tracking for a session."""
        expected_agents = await self._get_pipeline_agents(session_id)
        self.pipeline_progress[session_id] = {
            "expected_agents": expected_agents,
            "completed_agents": set(),
            "agent_outputs": {}
        }
        self.logger.info(
            "Initialized pipeline tracking",
            action="_initialize_pipeline_tracking",
            input_data={"session_id": session_id, "expected_agents": list(expected_agents)},
            layer="orchestrator_v2",
            step="init_pipeline_tracking"
        )

    async def _record_agent_completion(self, session_id: str, agent_name: str, output: dict): # to be removed
        """Record an agent completion and check if pipeline is complete."""
        if session_id not in self.pipeline_progress:
            await self._initialize_pipeline_tracking(session_id)
        
        progress = self.pipeline_progress[session_id]
        progress["completed_agents"].add(agent_name)
        progress["agent_outputs"][agent_name] = output
        
        self.logger.info(
            f"Recorded completion for {agent_name}",
            action="_record_agent_completion",
            input_data={"session_id": session_id, "agent_name": agent_name},
            output_data={
                "completed_agents": list(progress["completed_agents"]),
                "expected_agents": list(progress["expected_agents"]),
                "is_complete": await self._is_pipeline_complete(session_id)
            },
            layer="orchestrator_v2",
            step="record_agent_completion"
        )

    async def _make_final_decision(self, session_id: str, state_manager: StateManager, memory_manager: MemoryManager): # to be removed
        """Make the final decision after the complete pipeline finishes."""
        progress = self.pipeline_progress.get(session_id, {})
        agent_outputs = progress.get("agent_outputs", {})
        
        # Enhanced logging of pipeline completion
        self.logger.info(
            "Pipeline completed, analyzing agent outputs",
            action="_make_final_decision",
            input_data={
                "session_id": session_id,
                "completed_agents": list(progress.get("completed_agents", set())),
                "expected_agents": list(progress.get("expected_agents", set())),
                "agent_outputs_keys": list(agent_outputs.keys())
            },
            layer="orchestrator_v2",
            step="pipeline_analysis"
        )
        
        # Get context from memory
        context = await memory_manager.get_all_contextual()
        persistent = await memory_manager.get_all_persistent()
        workflow = persistent.get("workflow")
        conversation = context.get("conversation", [])
        user_turns = [turn for turn in conversation if turn["role"] == "user"]
        ai_turns = [turn for turn in conversation if turn["role"] == "ai"]
        
        # Get user query from contextual memory - prioritize STT output, then conversation history
        user_query = ""
        
        # First, try to get from STT agent output
        stt_output = agent_outputs.get("stt_agent", {})
        if stt_output and isinstance(stt_output, dict):
            stt_text = stt_output.get("outputs", {}).get("text", "")
            if stt_text:
                user_query = stt_text
                self.logger.info(
                    f"Using user query from STT output: {user_query}",
                    action="_make_final_decision",
                    input_data={"source": "stt_agent_output"},
                    layer="orchestrator_v2",
                    step="get_user_query"
                )
        
        # If no STT output, try conversation history
        if not user_query and user_turns:
            user_query = user_turns[-1]["text"]
            self.logger.info(
                f"Using user query from conversation history: {user_query}",
                action="_make_final_decision",
                input_data={"source": "conversation_history"},
                layer="orchestrator_v2",
                step="get_user_query"
            )
        
        # If still no user query, try contextual memory
        if not user_query and context.get("user_query"):
            user_query = context["user_query"]
            self.logger.info(
                f"Using user query from contextual memory: {user_query}",
                action="_make_final_decision",
                input_data={"source": "contextual_memory"},
                layer="orchestrator_v2",
                step="get_user_query"
            )
        
        # Enhanced agent response extraction with detailed logging
        agent_responses = []
        
        # Check STT agent output
        stt_output = agent_outputs.get("stt_agent", {})
        if stt_output:
            stt_text = stt_output.get("outputs", {}).get("text", "")
            if stt_text:
                agent_responses.append(f"STT: {stt_text}")
                self.logger.info(
                    f"STT agent output: {stt_text}",
                    action="_make_final_decision",
                    input_data={"agent": "stt_agent"},
                    layer="orchestrator_v2",
                    step="stt_analysis"
                )
        
        # Check preprocessing agent output
        preprocessing_output = agent_outputs.get("preprocessing_agent", {})
        if preprocessing_output:
            intent = preprocessing_output.get("outputs", {}).get("intent", "")
            clean_text = preprocessing_output.get("outputs", {}).get("clean_text", "")
            if intent:
                agent_responses.append(f"Intent: {intent}")
            if clean_text:
                agent_responses.append(f"Clean text: {clean_text}")
            self.logger.info(
                f"Preprocessing agent output: intent={intent}, clean_text={clean_text}",
                action="_make_final_decision",
                input_data={"agent": "preprocessing_agent"},
                layer="orchestrator_v2",
                step="preprocessing_analysis"
            )
        
        # Check processing agent output
        processing_output = agent_outputs.get("processing_agent", {})
        if processing_output:
            llm_answer = processing_output.get("outputs", {}).get("llm_answer", "")
            if llm_answer:
                agent_responses.append(f"LLM Answer: {llm_answer}")
                self.logger.info(
                    f"Processing agent output: {llm_answer[:100]}...",
                    action="_make_final_decision",
                    input_data={"agent": "processing_agent"},
                    layer="orchestrator_v2",
                    step="processing_analysis"
                )
        
        # Check TTS agent output
        tts_output = agent_outputs.get("tts_agent", {})
        if tts_output:
            audio_path = tts_output.get("outputs", {}).get("audio_path", "")
            if audio_path:
                agent_responses.append("Audio response generated")
                self.logger.info(
                    f"TTS agent output: {audio_path}",
                    action="_make_final_decision",
                    input_data={"agent": "tts_agent"},
                    layer="orchestrator_v2",
                    step="tts_analysis"
                )
        
        # Check filler TTS agent output
        filler_tts_output = agent_outputs.get("filler_tts_agent", {})
        if filler_tts_output:
            filler_audio_path = filler_tts_output.get("outputs", {}).get("audio_path", "")
            if filler_audio_path:
                agent_responses.append("Filler audio generated")
                self.logger.info(
                    f"Filler TTS agent output: {filler_audio_path}",
                    action="_make_final_decision",
                    input_data={"agent": "filler_tts_agent"},
                    layer="orchestrator_v2",
                    step="filler_tts_analysis"
                )
        
        agent_confidence = context.get("agent_confidence", None)
        current_state = state_manager.current_state_id
        
        # Prohibited action check
        prohibited_actions = workflow.get("prohibited_actions", []) if workflow else []
        is_prohib, matched_action = self.is_prohibited(user_query, prohibited_actions)
        if is_prohib:
            abort_message = f"Sorry, your request ('{matched_action}') is not allowed for security reasons. This session will be terminated."
            await self._cleanup_session(session_id, "orchestrator_v2", "Prohibited action abort")
            self.logger.info(
                abort_message,
                action="prohibited_action_abort",
                input_data={"user_query": user_query, "matched_action": matched_action},
                layer="orchestrator_v2",
                step="prohibited_action_abort"
            )
            return
        
        # Log comprehensive decision inputs
        self.logger.info(
            "Making final decision after complete pipeline",
            action="_make_final_decision",
            input_data={
                "session_id": session_id, 
                "completed_agents": list(progress["completed_agents"]),
                "user_query": user_query,
                "user_query_source": "STT output" if agent_outputs.get("stt_agent") else "conversation history" if user_turns else "contextual memory" if context.get("user_query") else "none",
                "agent_responses_count": len(agent_responses),
                "agent_responses": agent_responses,
                "agent_confidence": agent_confidence,
                "current_state": current_state
            },
            layer="orchestrator_v2",
            step="before_final_decision"
        )
        
        decision = await self.get_openai_decision(user_query, agent_responses, agent_confidence, current_state, workflow)
        
        self.logger.info(
            f"Final decision made: {decision}",
            action="_make_final_decision",
            input_data={"session_id": session_id},
            output_data={
                "decision": decision,
                "user_query": user_query,
                "agent_responses": agent_responses,
                "agent_confidence": agent_confidence
            },
            layer="orchestrator_v2",
            step="after_final_decision"
        )
        
        if decision == "proceed":
            state_config = state_manager.get_state(current_state)
            next_state_id = state_config.transitions[0].target if state_config and state_config.transitions else current_state
        else:
            next_state_id = current_state
        
        await state_manager.transition(next_state_id)
        
        verdict = f"Final decision: {decision}. Transitioning to {next_state_id}."
        decision_status = "success" if decision == "proceed" else "redo"
        
        self.logger.info(
            verdict,
            action="_make_final_decision",
            input_data={"session_id": session_id},
            output_data={"current_state": current_state, "next_state": next_state_id},
            status=decision_status,
            layer="orchestrator_v2",
            step="final_transition",
            next_state_id=next_state_id
        )
        
        # Publish state update
        state_update = A2AMessage(
            message_id=str(uuid.uuid4()),
            session_id=session_id,
            message_type=MessageType.NOTIFICATION,
            source_agent="orchestrator_v2",
            target_agent="all",
            payload={"current_state": current_state, "next_state": next_state_id, "verdict": verdict},
            schema_version=self.schema_version
        )
        await self.redis.publish("state_update", state_update.to_json())
        
        # Clear pipeline progress for this session
        self.pipeline_progress.pop(session_id, None)
        
        # Persist session state
        await self._persist_session_state(session_id, state_manager)

    async def handle_agent_completion(self, message: str):
        """Handle agent completion messages."""
        trace_id = str(uuid.uuid4())
        try:
            a2a_message = A2AMessage.from_json(message)
            if a2a_message.message_type != MessageType.NOTIFICATION:
                self.logger.error(
                    "Invalid message type",
                    action="handle_agent_completion",
                    input_data={"message": message},
                    reason="Expected NOTIFICATION message type",
                    trace_id=trace_id,
                    layer="orchestrator_v2",
                    step="validate_message"
                )
                return
            
            session_id = a2a_message.session_id
            agent_name = a2a_message.source_agent
            output = a2a_message.payload
            
            if not all([session_id, agent_name, output]):
                self.logger.error(
                    "Missing required message fields",
                    action="handle_agent_completion",
                    input_data={"message": message},
                    reason="Missing session_id, source_agent, or payload",
                    trace_id=trace_id,
                    layer="orchestrator_v2",
                    step="validate_message"
                )
                return
            
            # Get or create StateManager
            state_manager = await self._get_or_create_state_manager(session_id)
            memory_manager = MemoryManager(session_id=session_id, user_id=state_manager.user_id)
            
            # Check retry count
            retry_count = await self._get_retry_count(session_id, agent_name)
            if retry_count >= self.max_retries:
                verdict = f"Maximum retries ({self.max_retries}) reached, shutting down session"
                self.logger.error(
                    verdict,
                    action="handle_agent_completion",
                    input_data={"session_id": session_id, "agent_name": agent_name, "retry_count": retry_count},
                    trace_id=trace_id,
                    layer="orchestrator_v2",
                    step="check_retries"
                )
                state_update = A2AMessage(
                    message_id=str(uuid.uuid4()),
                    session_id=session_id,
                    message_type=MessageType.NOTIFICATION,
                    source_agent="orchestrator_v2",
                    target_agent="all",
                    payload={"current_state": state_manager.current_state_id, "next_state": None, "verdict": verdict},
                    schema_version=self.schema_version
                )
                await self.redis.publish("state_update", state_update.to_json())
                await self._cleanup_session(session_id, agent_name, verdict)
                return
            
            # Store agent output in memory for context
            await memory_manager.set("contextual", f"{agent_name}_output", output) # should be removed
            
            # Record agent completion
            await self._record_agent_completion(session_id, agent_name, output) # to be removed
            
            # Enhanced logging of agent completion
            self.logger.info(
                f"Agent {agent_name} completed with output keys: {list(output.keys()) if isinstance(output, dict) else 'non-dict'}",
                action="handle_agent_completion",
                input_data={
                    "session_id": session_id, 
                    "agent_name": agent_name,
                    "output_keys": list(output.keys()) if isinstance(output, dict) else None,
                    "output_type": type(output).__name__
                },
                layer="orchestrator_v2",
                step="agent_completion_log"
            )
            
            # Check if pipeline is complete
            if await self._is_pipeline_complete(session_id): # to be removed
                self.logger.info(
                    "Pipeline complete, making final decision",
                    action="handle_agent_completion",
                    input_data={"session_id": session_id, "agent_name": agent_name},
                    layer="orchestrator_v2",
                    step="pipeline_complete"
                )
                await self._make_final_decision(session_id, state_manager, memory_manager)
            else:
                progress = self.pipeline_progress.get(session_id, {})
                self.logger.info(
                    f"Agent {agent_name} completed, pipeline still in progress",
                    action="handle_agent_completion",
                    input_data={
                        "session_id": session_id, 
                        "agent_name": agent_name,
                        "completed_agents": list(progress.get("completed_agents", set())),
                        "expected_agents": list(progress.get("expected_agents", set())),
                        "remaining_agents": list(progress.get("expected_agents", set()) - progress.get("completed_agents", set()))
                    },
                    layer="orchestrator_v2",
                    step="pipeline_in_progress"
                )
            
        except ValidationError as e:
            self.logger.error(
                "Message schema validation failed",
                action="handle_agent_completion",
                input_data={"message": message},
                reason=str(e),
                trace_id=trace_id,
                layer="orchestrator_v2",
                step="validate_message"
            )
        except Exception as e:
            self.logger.error(
                "Error processing agent completion",
                action="handle_agent_completion",
                input_data={"message": message},
                reason=str(e),
                trace_id=trace_id,
                layer="orchestrator_v2",
                step="handle_agent_completion"
            )

    async def _get_or_create_state_manager(self, session_id: str) -> StateManager:
        """Get or create a StateManager for the session."""
        if session_id not in self.sessions:
            state_data = await self.redis.get(f"session:{session_id}:state")
            if state_data:
                state_dict = json.loads(state_data)
                state_manager = await StateManager.create(
                    workflow_name=self.workflow_name,
                    session_id=session_id,
                    user_id=state_dict.get("user_id")
                )
                state_manager.current_state_id = state_dict.get("current_state_id", "initial_state")
                await state_manager.memory_manager.set("contextual", "output", state_dict.get("memory", {}))
                self.logger.debug(
                    "Restored session state from Redis",
                    action="get_or_create_state_manager",
                    input_data={"session_id": session_id},
                    output_data={"current_state_id": state_manager.current_state_id},
                    status="success",
                    layer="orchestrator_v2",
                    step="restore_session"
                )
            else:
                state_manager = await StateManager.create(workflow_name=self.workflow_name, session_id=session_id)
                self.logger.info(
                    "Initialized new session",
                    action="get_or_create_state_manager",
                    input_data={"session_id": session_id},
                    output_data={"current_state_id": state_manager.current_state_id},
                    status="success",
                    layer="orchestrator_v2",
                    step="create_session"
                )
            self.sessions[session_id] = state_manager
        return self.sessions[session_id]

    async def _persist_session_state(self, session_id: str, state_manager: StateManager):
        """Persist session state to Redis."""
        try:
            state_data = {
                "current_state_id": state_manager.current_state_id,
                "memory": await state_manager.memory_manager.get("contextual", "output", {}),
                "user_id": state_manager.user_id
            }
            await self.redis.set(f"session:{session_id}:state", json.dumps(state_data), ex=3600)
            self.logger.debug(
                "Persisted session state to Redis",
                action="persist_session_state",
                input_data={"session_id": session_id},
                output_data={"current_state_id": state_manager.current_state_id},
                status="success",
                layer="orchestrator_v2",
                step="persist_session"
            )
        except Exception as e:
            self.logger.error(
                "Failed to persist session state",
                action="persist_session_state",
                input_data={"session_id": session_id},
                reason=str(e),
                layer="orchestrator_v2",
                step="persist_session"
            )

    async def _get_retry_count(self, session_id: str, agent_name: str) -> int: # to be removed
        """Get retry count for an agent."""
        key = f"session:{session_id}:retries:{agent_name}"
        count = await self.redis.get(key)
        return int(count) if count else 0

    async def _increment_retry_count(self, session_id: str, agent_name: str): # to be removed
        """Increment retry count for an agent."""
        key = f"session:{session_id}:retries:{agent_name}"
        await self.redis.incr(key)
        await self.redis.expire(key, 3600)

    async def _reset_retry_count(self, session_id: str, agent_name: str): # to be removed
        """Reset retry count for an agent."""
        key = f"session:{session_id}:retries:{agent_name}"
        await self.redis.delete(key)

    async def save_dialog_log(self, session_id: str):
        """Save dialog log for a session."""
        try:
            state_manager = self.sessions.get(session_id)
            if state_manager and state_manager.memory_manager:
                await state_manager.memory_manager.save_dialog_log()
                self.logger.info(
                    "Successfully saved dialog log",
                    action="save_dialog_log",
                    input_data={"session_id": session_id, "user_id": state_manager.user_id},
                    status="success",
                    layer="orchestrator_v2",
                    step="save_dialog_log"
                )
            else:
                self.logger.warning(
                    "No state manager or memory manager found for session",
                    action="save_dialog_log",
                    input_data={"session_id": session_id},
                    status="warning",
                    layer="orchestrator_v2",
                    step="save_dialog_log"
                )
        except Exception as e:
            self.logger.error(
                "Failed to save dialog log",
                action="save_dialog_log",
                input_data={"session_id": session_id},
                reason=str(e),
                layer="orchestrator_v2",
                step="save_dialog_log"
            )

    async def _cleanup_session(self, session_id: str, agent_name: str, reason: str):
        """Clean up session data."""
        try:
            state_manager = self.sessions.get(session_id)
            if state_manager and state_manager.memory_manager:
                await state_manager.memory_manager.save_dialog_log()
            
            # Use scan instead of keys to avoid blocking Redis
            try:
                # Get all keys for this session using scan
                keys = []
                cursor = 0
                while True:
                    cursor, batch = await self.redis.client.scan(cursor, match=f"session:{session_id}:*", count=100)
                    keys.extend(batch)
                    if cursor == 0:
                        break
                
                if keys:
                    await self.redis.delete(*keys)
            except Exception as e:
                self.logger.warning(
                    f"Could not clean up Redis keys for session {session_id}: {e}",
                    action="cleanup_session",
                    input_data={"session_id": session_id},
                    layer="orchestrator_v2",
                    step="cleanup_redis_keys"
                )
            
            self.sessions.pop(session_id, None)
            # Clean up session metadata
            self.session_metadata.pop(session_id, None)
            # Clean up pipeline progress
            self.pipeline_progress.pop(session_id, None)
            self.logger.info(
                f"Cleaned up session data: {reason}",
                action="cleanup_session",
                input_data={"session_id": session_id, "agent_name": agent_name},
                status="success",
                layer="orchestrator_v2",
                step="cleanup_session"
            )
        except Exception as e:
            self.logger.error(
                "Failed to clean up session data",
                action="cleanup_session",
                input_data={"session_id": session_id, "agent_name": agent_name},
                reason=str(e),
                layer="orchestrator_v2",
                step="cleanup_session"
            )

    async def close(self):
        """Close the orchestrator."""
        self.running = False
        if self._listen_task:
            self._listen_task.cancel()
            try:
                await self._listen_task
            except asyncio.CancelledError:
                pass
        await self.redis.close()
        self.logger.info(
            "Closed Redis connection",
            action="close",
            status="success",
            layer="orchestrator_v2",
            step="close_redis"
        )
        # self.logger.flush()

    async def orch_Start(self):
        """Start the orchestrator with proper workflow execution."""
        try:
            await self.start()
            
            # Keep running until shutdown
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            self.logger.error(
                "OrchestratorV2 startup failed",
                action="main",
                reason=str(e),
                layer="orchestrator_v2",
                step="start"
            )
        finally:
            # Clean up all sessions
            for session_id, state_manager in list(self.sessions.items()):
                await self._cleanup_session(session_id, "orchestrator_v2", "OrchestratorV2 shutdown")
            await self.close()

    async def run_session(self, workflow_name, audio_file, session_id=None): # retrieve audio file and session id from contextual memory, remove audio file no need for it, stt agent will fetch it
        """Run a session with the specified workflow and audio file."""
        if session_id is None:
            session_id = f"test_fillerwords_{uuid.uuid4().hex[:8]}"
        self.workflow_name = workflow_name
        
        # Store session metadata including audio file path
        self.session_metadata[session_id] = {
            "audio_file": audio_file,
            "workflow_name": workflow_name
        }
        
        print(f"\n=== OrchestratorV2 Integration Test: {session_id} | Audio: {audio_file} ===")
        
        # Start orchestrator
        orch_task = asyncio.create_task(self.orch_Start())
        await asyncio.sleep(2)
        
        # Get or create state manager for this session
        state_manager = await self._get_or_create_state_manager(session_id)
        
        # Store audio file in session metadata
        self.session_metadata[session_id] = {
            "audio_file": audio_file,
            "workflow_name": workflow_name
        }
        
        # Execute the first state to start the workflow
        self.logger.info(
            f"Starting workflow execution for session {session_id}",
            action="run_session",
            input_data={"session_id": session_id, "workflow_name": workflow_name, "audio_file": audio_file},
            layer="orchestrator_v2",
            step="start_session"
        )
        
        # Initialize pipeline tracking before executing the state
        await self._initialize_pipeline_tracking(session_id)
        
        # Execute the first state to trigger the Layer2 pipeline
        # This will run all agents in the pipeline and they will send completion notifications
        await state_manager.execute_step({
            "audio_path": audio_file
        })
        try:
            # Wait for pipeline completion with timeout
            timeout_seconds = 30  # Increased timeout for full pipeline
            start_time = asyncio.get_event_loop().time()
            
            while not await self._is_pipeline_complete(session_id):
                if asyncio.get_event_loop().time() - start_time > timeout_seconds:
                    self.logger.warning(
                        f"Pipeline timeout after {timeout_seconds} seconds",
                        action="run_session",
                        input_data={"session_id": session_id},
                        layer="orchestrator_v2",
                        step="pipeline_timeout"
                    )
                    break
                await asyncio.sleep(1)
                
        except asyncio.TimeoutError:
            self.logger.warning(
                "Pipeline execution timed out",
                action="run_session",
                input_data={"session_id": session_id},
                layer="orchestrator_v2",
                step="timeout"
            )
        finally:
            print(f"\n=== Testing Dialog Saving for session: {session_id} ===")
            try:
                await self.save_dialog_log(session_id)
                print(f"[SUCCESS] Dialog log saved for session: {session_id}")
            except Exception as e:
                print(f"[ERROR] Failed to save dialog log for session {session_id}: {e}")
            orch_task.cancel()
            await asyncio.gather(orch_task, return_exceptions=True)
        memory = MemoryManager(session_id)
        print("\n=== Final Contextual Memory ===")
        print(await memory.get_all_contextual())
        print("\n=== Conversation History ===")
        print(await memory.get_conversation())
        print(f"\n[TEST] Finished integration for session: {session_id}")
        print(f"[INFO] Dialog logs are automatically saved by the OrchestratorV2 during session lifecycle")
        print(f"{'='*60}\n")