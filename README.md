# Voice-Agents-Platform

For more information, visit the project's Notion page (Current Projects -> Voice AI Agents -> Plan Section)

# Repository Structure

## Project Root

This is the root of your voice agent project. It follows a **modular, clean architecture** designed for scalability, maintainability, and low-latency execution. Here's how it breaks down:

---

### 🧠 `agents/` — Core Functional Brains

This folder contains **AI agents**, each responsible for a major function. They use **MCP (Modular Communication Protocol)** and can communicate using **A2A (Agent-to-Agent) messages** through method calls or shared memory (Redis).

| File                | Role                                                                                                                               |
| ------------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| `voice_agent.py`    | Orchestrator. It manages the voice interaction loop, queries other agents, and routes decisions.                                   |
| `audio_agent.py`    | Converts speech to text (STT), detects voice activity (VAD), cleans audio (noise filtering), and synthesizes text to speech (TTS). |
| `disambiguator.py`  | Fixes language issues, determines language, gender, and normalizes ambiguous phrases. Supports multilingual conversion.            |
| `response_agent.py` | Parses intents, triggers RAG if needed, queries the LLM, and returns a proper response.                                            |

---

### 🧩 `core/` — Infrastructure Logic

This layer handles the runtime mechanics, session memory, logging, and task execution. It's reusable and not tied to any specific agent.

| File                 | Purpose                                                                                                 |
| -------------------- | ------------------------------------------------------------------------------------------------------- |
| `pipeline.py`        | Manages the ordered execution of steps in a conversation turn (simplified DAG). Configurable from YAML. |
| `agent_interface.py` | Standardizes how agents send/receive A2A messages. Includes message signatures and MCP structure.       |
| `session_context.py` | Manages per-user state using Redis. Supports reading/writing memory during conversations.               |
| `rag_store.py`       | Talks to Qdrant vector DB, handles KB search and RAG fallbacks. Also logs feedback on failed queries.   |
| `logger.py`          | Global logging utility. Logs with timestamps, agent name, latency, token count, and system state.       |

---

### ⚙️ `config/` — Agent & System Settings

All settings are stored in simple YAML files to make customization easier for non-technical team members.

| File             | Description                                                                          |
| ---------------- | ------------------------------------------------------------------------------------ |
| `settings.yaml`  | Redis, Qdrant, STT/TTS backend settings, voice agent language, and timeouts.         |
| `agents.yaml`    | Enables/disables agents, configures thresholds, timeout behavior, fallback settings. |
| `languages.yaml` | List of supported languages, locales, and priority routing rules.                    |
| `voices.yaml`    | Mapping between TTS voices and detected gender/language combinations.                |

---

### 🪵 `logs/` — Runtime Audit Trail

This folder contains **all runtime data** that may be needed for debugging, performance evaluation, or user support.

| Folder/File      | Contents                                                                      |
| ---------------- | ----------------------------------------------------------------------------- |
| `conversations/` | Full transcripts of conversations (text) indexed by session ID and timestamp. |
| `metrics/`       | Token usage, latency per agent, session durations. Used for optimization.     |
| `errors/`        | Stack traces and system failure logs with timestamps and error metadata.      |

---

### 🌐 `ui/` — Visualization and Debug Tools

Optional tools to help visualize conversation states and debugging:

| File/Folder             | Description                                                                                   |
| ----------------------- | --------------------------------------------------------------------------------------------- |
| `frontend/`             | Web UI (React or basic HTML) to see live session info. Can show per-turn state, latency, etc. |
| `conversation_graph.js` | Shows a node graph that lights up when a state (agent) is active during a session.            |

---

### 🛠️ `utils/` — Helpers

These are shared tools and utility modules used across the system.

| File                | Purpose                                                                            |
| ------------------- | ---------------------------------------------------------------------------------- |
| `language_utils.py` | Gender, language, and dialect detection. Also includes translation helpers.        |
| `audio_utils.py`    | Voice Activity Detection, silence trimming, noise filtering, volume normalization. |
| `cache.py`          | Optional STT/TTS caching with hash-based keys.                                     |
| `timers.py`         | Used to benchmark latency per module/step.                                         |

---

### ✅ `tests/` — Unit & Workflow Tests

Test the entire system or specific agents and modules.

| File                | Description                                                  |
| ------------------- | ------------------------------------------------------------ |
| `test_workflows.py` | Simulates real user sessions end-to-end.                     |
| `test_memory.py`    | Tests Redis read/write operations and edge cases.            |
| `test_agents.py`    | Unit tests for logic inside each agent.                      |
| `test_rag.py`       | Verifies knowledge base queries and RAG feedback mechanisms. |

---

### 📜 `scripts/` — CLI Tools

Command-line utilities that help with one-time or dev tasks.

| File                   | Description                                            |
| ---------------------- | ------------------------------------------------------ |
| `load_kb_from_dir.py`  | Embeds Markdown/PDF/TXT documents into Qdrant locally. |
| `simulate_call.py`     | Run a test voice session from an audio file.           |
| `export_logs.py`       | Export logs for debugging or training.                 |
| `run_debug_session.py` | Manual test session with detailed step logs.           |

---

### 🐳 Docker & Bootstrapping

| File                 | Description                                      |
| -------------------- | ------------------------------------------------ |
| `Dockerfile`         | Main app container. Runs the voice agent system. |
| `docker-compose.yml` | Starts Redis, Qdrant, and the bot together.      |
| `.env`               | Environment secrets like API keys and hostnames. |

## MongoDB with Docker

This project includes a MongoDB service via Docker Compose. To start MongoDB:

```bash
docker-compose up -d mongodb
```

- MongoDB will be available at `localhost:27017`.
- Default username: `root`
- Default password: `example`

To connect from your application, use:

```
**************************************/
```

To stop MongoDB:

```bash
docker-compose stop mongodb
```
