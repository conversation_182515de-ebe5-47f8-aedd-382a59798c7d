from typing import Optional, Union
from pydantic import BaseModel, Field

# --- Input/Output Schema Definitions for Pipeline States V2 ---

class STTInputSchema(BaseModel):
    """Schema for STT State input validation"""
    audio_path: Union[str, bytes] = Field(..., description="Path to audio file or audio bytes")

class STTOutputSchema(BaseModel):
    """Schema for STT State output validation"""
    text: str = Field(..., description="Transcribed text from audio")
    latencySTT: int = Field(..., description="Processing latency in milliseconds")

class PreProcessingInputSchema(BaseModel):
    """Schema for PreProcessing State input validation"""
    transcript: str = Field(..., min_length=1, description="Raw transcript text to process")

class PreProcessingOutputSchema(BaseModel):
    """Schema for PreProcessing State output validation"""
    clean_text: str = Field(..., description="Cleaned and processed text")
    intent: str = Field(..., description="Detected user intent")
    emotion: Optional[str] = Field(None, description="Detected emotion")
    gender: Optional[str] = Field(None, description="Detected gender")
    latencyPreprocessing: int = Field(..., description="Processing latency in milliseconds")

class ProcessingInputSchema(BaseModel):
    """Schema for Processing State input validation"""
    clean_text: str = Field(..., min_length=1, description="Clean text to process")
    intent: str = Field(..., description="User intent for processing")

class ProcessingOutputSchema(BaseModel):
    """Schema for Processing State output validation"""
    llm_answer: str = Field(..., description="LLM generated response")
    latencyProcessing: int = Field(..., description="Processing latency in milliseconds")

class FillerInputSchema(BaseModel):
    """Schema for Filler State input validation"""
    filler_text: Optional[str] = Field(None, description="Optional filler text to use")

class FillerOutputSchema(BaseModel):
    """Schema for Filler State output validation"""
    audio_path: str = Field(..., description="Path to generated filler audio")
    filler_text: str = Field(..., description="Text used for filler generation")

class TTSInputSchema(BaseModel):
    """Schema for TTS State input validation"""
    text: str = Field(..., min_length=1, description="Text to convert to speech")
    emotion: str = Field(..., description="Emotion for speech synthesis")
    gender: str = Field(..., description="Gender for voice selection")

class TTSOutputSchema(BaseModel):
    """Schema for TTS State output validation"""
    audio_path: str = Field(..., description="Path to generated audio file")
    latencyTTS: int = Field(..., description="Processing latency in milliseconds") 