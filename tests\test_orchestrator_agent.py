import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from agents.orchestrator_agent import Orchestrator
from schemas.a2a_message import A2AMessage, MessageType
from utils.redis_client import RedisClient
from core.state_mgmt.StateManager import StateManager

@pytest_asyncio.fixture
def orchestrator():
    orch = Orchestrator()
    orch.redis = AsyncMock()
    return orch

@pytest_asyncio.fixture
def mock_state_manager():
    sm = AsyncMock()
    sm.memory_manager = MagicMock()
    sm.memory_manager.get_all_contextual = AsyncMock()
    sm.memory_manager.get_all_persistent = AsyncMock()
    sm.get_state = MagicMock()
    sm.transition = AsyncMock()
    sm.end_session_cleanup = AsyncMock()
    return sm

@pytest.mark.asyncio
async def test_proceed_decision(monkeypatch, orchestrator, mock_state_manager):
    # Setup context and persistent memory
    context = {
        "conversation": [
            {"role": "user", "text": "What is my balance?"},
            {"role": "ai", "text": "Please provide your account ID."},
            {"role": "user", "text": "12345"},
            {"role": "ai", "text": "Your balance is $1000."}
        ],
        "agent_confidence": 0.95
    }
    persistent = {
        "workflow": {
            "prohibited_actions": ["Do not share account name and password"],
            "allowed_actions": ["Checking account balance"],
            "states": {
                "state_1": {"transitions": [{"target": "state_2"}]},
                "state_2": {"transitions": []}
            }
        }
    }
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[MagicMock(target="state_2")])

    # Patch Gemini decision to 'proceed'
    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="proceed"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    # Compose a valid A2AMessage
    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    await orchestrator.handle_agent_completion(msg)
    mock_state_manager.transition.assert_awaited_with("state_2")

@pytest.mark.asyncio
async def test_redo_decision(monkeypatch, orchestrator, mock_state_manager):
    context = {
        "conversation": [
            {"role": "user", "text": "What is my balance?"},
            {"role": "ai", "text": "Please provide your account ID."},
            {"role": "user", "text": "12345"},
            {"role": "ai", "text": "Your balance is $1000."}
        ],
        "agent_confidence": 0.5
    }
    persistent = {
        "workflow": {
            "prohibited_actions": ["Do not share account name and password"],
            "allowed_actions": ["Checking account balance"],
            "states": {
                "state_1": {"transitions": [{"target": "state_2"}]},
                "state_2": {"transitions": []}
            }
        }
    }
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[MagicMock(target="state_2")])

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="redo"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    await orchestrator.handle_agent_completion(msg)
    mock_state_manager.transition.assert_awaited_with("state_1")

@pytest.mark.asyncio
async def test_prohibited_action(monkeypatch, orchestrator, mock_state_manager):
    context = {
        "conversation": [
            {"role": "user", "text": "I want to share my account name and password."},
            {"role": "ai", "text": "Please do not share sensitive information."}
        ],
        "agent_confidence": 0.9
    }
    persistent = {
        "workflow": {
            "prohibited_actions": ["Do not share account name and password"],
            "allowed_actions": ["Checking account balance"],
            "states": {
                "state_1": {"transitions": [{"target": "state_2"}]},
                "state_2": {"transitions": []}
            }
        }
    }
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[MagicMock(target="state_2")])

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock())
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    await orchestrator.handle_agent_completion(msg)
    mock_state_manager.transition.assert_not_awaited()
    mock_state_manager.end_session_cleanup.assert_awaited()

@pytest.mark.asyncio
def test_no_transitions(monkeypatch, orchestrator, mock_state_manager):
    context = {
        "conversation": [
            {"role": "user", "text": "What is my balance?"},
            {"role": "ai", "text": "Please provide your account ID."}
        ]
    }
    persistent = {
        "workflow": {
            "prohibited_actions": [],
            "allowed_actions": ["Checking account balance"],
            "states": {
                "state_1": {"transitions": []}
            }
        }
    }
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[])

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="proceed"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    # Should stay in current state
    import asyncio
    asyncio.run(orchestrator.handle_agent_completion(msg))
    mock_state_manager.transition.assert_awaited_with("state_1")

@pytest.mark.asyncio
def test_empty_conversation(monkeypatch, orchestrator, mock_state_manager):
    context = {"conversation": []}
    persistent = {
        "workflow": {
            "prohibited_actions": [],
            "allowed_actions": ["Checking account balance"],
            "states": {"state_1": {"transitions": [{"target": "state_2"}]}}
        }
    }
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[MagicMock(target="state_2")])

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="proceed"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    # Should not crash, should stay in current state
    import asyncio
    asyncio.run(orchestrator.handle_agent_completion(msg))
    mock_state_manager.transition.assert_awaited_with("state_2")

@pytest.mark.asyncio
def test_missing_workflow(monkeypatch, orchestrator, mock_state_manager):
    context = {"conversation": [{"role": "user", "text": "What is my balance?"}]}
    persistent = {}  # No workflow key
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[MagicMock(target="state_2")])

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="proceed"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    # Should not crash, should stay in current state
    import asyncio
    asyncio.run(orchestrator.handle_agent_completion(msg))
    mock_state_manager.transition.assert_awaited_with("state_2")

@pytest.mark.asyncio
def test_missing_agent_confidence(monkeypatch, orchestrator, mock_state_manager):
    context = {
        "conversation": [
            {"role": "user", "text": "What is my balance?"},
            {"role": "ai", "text": "Please provide your account ID."}
        ]
        # No agent_confidence
    }
    persistent = {
        "workflow": {
            "prohibited_actions": [],
            "allowed_actions": ["Checking account balance"],
            "states": {"state_1": {"transitions": [{"target": "state_2"}]}}
        }
    }
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[MagicMock(target="state_2")])

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="proceed"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    # Should not crash, should transition
    import asyncio
    asyncio.run(orchestrator.handle_agent_completion(msg))
    mock_state_manager.transition.assert_awaited_with("state_2")

@pytest.mark.asyncio
def test_prohibited_action_similarity_threshold(monkeypatch, orchestrator, mock_state_manager):
    context = {
        "conversation": [
            {"role": "user", "text": "I want to share my account name and password."},  # typo
            {"role": "ai", "text": "Please do not share sensitive information."}
        ]
    }
    persistent = {
        "workflow": {
            "prohibited_actions": ["Do not share account name and password"],
            "allowed_actions": ["Checking account balance"],
            "states": {"state_1": {"transitions": [{"target": "state_2"}]}}
        }
    }
    mock_state_manager.memory_manager.get_all_contextual.return_value = context
    mock_state_manager.memory_manager.get_all_persistent.return_value = persistent
    mock_state_manager.current_state_id = "state_1"
    mock_state_manager.get_state.return_value = MagicMock(transitions=[MagicMock(target="state_2")])

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="proceed"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=mock_state_manager))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    # Should not abort, should transition
    import asyncio
    asyncio.run(orchestrator.handle_agent_completion(msg))
    mock_state_manager.transition.assert_awaited_with("state_2")

@pytest.mark.asyncio
def test_integration_full_flow(monkeypatch, orchestrator):
    # Simulate a full workflow with 3 states, Gemini always says 'proceed'
    sm = AsyncMock()
    sm.memory_manager = MagicMock()
    sm.memory_manager.get_all_contextual = AsyncMock()
    sm.memory_manager.get_all_persistent = AsyncMock()
    sm.get_state = MagicMock()
    sm.transition = AsyncMock()
    sm.current_state_id = "state_1"

    # Setup workflow
    persistent = {
        "workflow": {
            "prohibited_actions": [],
            "allowed_actions": ["Checking account balance"],
            "states": {
                "state_1": {"transitions": [{"target": "state_2"}]},
                "state_2": {"transitions": [{"target": "state_3"}]},
                "state_3": {"transitions": []}
            }
        }
    }
    context = {"conversation": [{"role": "user", "text": "Start"}]}
    sm.memory_manager.get_all_contextual.return_value = context
    sm.memory_manager.get_all_persistent.return_value = persistent

    # State transitions
    sm.get_state.side_effect = [
        MagicMock(transitions=[MagicMock(target="state_2")]),
        MagicMock(transitions=[MagicMock(target="state_3")]),
        MagicMock(transitions=[])
    ]
    sm.current_state_id = "state_1"

    monkeypatch.setattr(orchestrator, "get_gemini_decision", AsyncMock(return_value="proceed"))
    monkeypatch.setattr(orchestrator, "_get_or_create_state_manager", AsyncMock(return_value=sm))
    monkeypatch.setattr(orchestrator, "_get_retry_count", AsyncMock(return_value=0))
    monkeypatch.setattr(orchestrator, "_persist_session_state", AsyncMock())
    monkeypatch.setattr(orchestrator, "redis", AsyncMock())

    # Simulate 3 state transitions
    msg = A2AMessage(
        session_id="sess1",
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()

    import asyncio
    # state_1 -> state_2
    sm.current_state_id = "state_1"
    asyncio.run(orchestrator.handle_agent_completion(msg))
    sm.transition.assert_awaited_with("state_2")
    sm.transition.reset_mock()
    # state_2 -> state_3
    sm.current_state_id = "state_2"
    asyncio.run(orchestrator.handle_agent_completion(msg))
    sm.transition.assert_awaited_with("state_3")
    sm.transition.reset_mock()
    # state_3 (no transitions, should stay)
    sm.current_state_id = "state_3"
    asyncio.run(orchestrator.handle_agent_completion(msg))
    sm.transition.assert_awaited_with("state_3")

@pytest.mark.asyncio
@pytest.mark.integration
async def test_real_state_manager_redis_gemini_integration():
    """
    This test requires:
    - A running Redis instance (default: redis://localhost:6379)
    - A valid Gemini API key in .env as GEMINI_API_KEY
    """
    redis = RedisClient()
    session_id = "integration_test_session"
    user_id = "integration_test_user"
    workflow_name = "test_workflow.json"
    # Simulate a workflow config file in config/workflow_states_dir
    # For this test, we will mock the workflow config in persistent memory
    workflow = {
        "id": "test_workflow",
        "name": "Test Workflow",
        "version": "1.0",
        "start": "state_1",
        "allowed_actions": ["Checking account balance"],
        "prohibited_actions": ["Do not share account name and password"],
        "states": {
            "state_1": {"type": "normal", "layer2_id": "l2_1", "expected_input": [], "expected_output": [], "transitions": [{"condition": "true", "target": "state_2"}], "allowed_tools": []},
            "state_2": {"type": "normal", "layer2_id": "l2_2", "expected_input": [], "expected_output": [], "transitions": [], "allowed_tools": []}
        }
    }
    # Write workflow summary to persistent memory
    persistent_key = f"{user_id}:workflow_source_of_truth_{workflow['id']}"
    await redis.set(persistent_key, workflow)
    # Write context to Redis
    context = {
        "conversation": [
            {"role": "user", "text": "What is my balance?"},
            {"role": "ai", "text": "Please provide your account ID."},
            {"role": "user", "text": "12345"},
            {"role": "ai", "text": "Your balance is $1000."}
        ],
        "agent_confidence": 0.95
    }
    await redis.set(session_id, context)
    # Create a real StateManager instance
    sm = await StateManager.create(workflow_name="test_workflow.json", session_id=session_id, user_id=user_id)
    sm.current_state_id = "state_1"
    # Patch StateManager's workflow and memory_manager to use our test data
    sm.workflow = type("WorkflowWrapper", (), {"workflow": type("WorkflowConfig", (), workflow)()})()
    sm.memory_manager.contextual.get_all = AsyncMock(return_value=context)
    sm.memory_manager.get = AsyncMock(return_value=None)
    sm.memory_manager.set = AsyncMock()
    # Orchestrator setup
    orch = Orchestrator()
    orch.redis = redis
    orch.sessions[session_id] = sm
    # Compose a valid A2AMessage
    msg = A2AMessage(
        session_id=session_id,
        message_type=MessageType.NOTIFICATION,
        source_agent="TestAgent",
        target_agent="orchestrator",
        payload={"test": "data"},
        schema_version="1.0"
    ).to_json()
    # Run the orchestrator (this will call real Gemini and Redis)
    await orch.handle_agent_completion(msg)
    # Assert that StateManager transitioned to state_2
    assert sm.current_state_id == "state_2"
    # Clean up Redis keys
    await redis.delete(persistent_key)
    await redis.delete(session_id) 