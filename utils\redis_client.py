import redis.asyncio as aioredis
import json
import os
import asyncio
from typing import Any, Callable, Optional
from tenacity import retry, stop_after_attempt, stop_after_delay, wait_fixed, retry_if_exception_type, RetryError
from core.logger_config import get_module_logger
import re
from utils.audio_utils import synthesize_fallback_audio

class RedisClient:
    """
    An asynchronous Redis client utility for the AI Voice Agents Platform.

    This client provides a simple interface for key-value storage, pub/sub messaging,
    and list-based task queues, with automatic JSON serialization/deserialization.

    Example:
        >>> import asyncio
        >>> from utils.redis_client import RedisClient
        >>>
        >>> async def main():
        ...     redis = RedisClient()
        ...     await redis.set("my_key", {"data": "value"})
        ...     value = await redis.get("my_key")
        ...     print(value)
        ...     await redis.close()
        >>>
        >>> # To run this example: asyncio.run(main())
    """
    logger = get_module_logger("RedisClient")
    def __init__(self, url: str = None, default_ttl: int = None):
        """
        Initializes the RedisClient.

        Args:
            url (str): The connection URL for the Redis server.
            default_ttl (int, optional): Default TTL for keys in seconds.
        """
        if default_ttl is None:
            env_ttl = os.getenv("REDIS_DEFAULT_TTL")
            self.default_ttl = int(env_ttl) if env_ttl is not None else None
        else:
            self.default_ttl = default_ttl
        
        # Use environment variables for Redis connection
        if url is None:
            redis_host = os.getenv("REDIS_HOST", "localhost")
            redis_port = os.getenv("REDIS_PORT", "6379")
            url = f"redis://{redis_host}:{redis_port}"
        
        self.client = aioredis.from_url(url, decode_responses=True)
        self.pubsub = None
        self.logger = RedisClient.logger


    # TODOFallback mechanism for TTS agent
    @staticmethod
    def _fallback_response(retry_state):
        """
        Fallback mechanism returning a TTS agent response when retries fail.

        Args:
            retry_state: Tenacity retry state object.

        Returns:
            dict: Fallback response for TTS agent.
        """
        logger = RedisClient.logger
        logger.warning(
            "Operation failed after retries",
            action="fallback",
            input_data=None,
            output_data=None,
            status="fail",
            reason=str(retry_state.outcome.exception()),
            layer="utils",
            step="_fallback_response",
            agent_name="RedisClient"
        )
        fallback_text = "I'm really sorry, but can you repeat that please?"
        audio_path = synthesize_fallback_audio(fallback_text, session_id="redis_fallback")
        if audio_path:
            return {"tts_response": fallback_text, "tts_audio_path": audio_path}
        else:
            return {"tts_response": fallback_text}

    @retry(
    stop=(stop_after_attempt(3) | stop_after_delay(1)),  # Retry 3 times or timeout after 5 seconds
    wait=wait_fixed(1),  # Wait 1 second between retries
    retry=retry_if_exception_type((aioredis.RedisError, json.JSONDecodeError)),
    retry_error_callback=_fallback_response)
    async def get(self, key: str) -> Any:
        """
        Retrieves a value from Redis by key.
        If the value is a JSON string, it's deserialized into a Python object.

        Args:
            key (str): The key to retrieve.

        Returns:
            The value associated with the key, or None if the key doesn't exist.

        Example:
            >>> await redis.set("session_context", {"user_id": 123, "state": "active"})
            >>> context = await redis.get("session_context")
            >>> print(context['user_id'])
            123
        """
        session_id = self._extract_session_id_from_key(key)
        try:
            value = await self.client.get(key)
            if value is None:
                return None
            return json.loads(value)
        except json.JSONDecodeError:
            return value # Return as raw string if not valid JSON
        except aioredis.RedisError as e:
            self.logger.log(
                level="error",
                message=f"Redis error getting key '{key}': {e}",
                session_id=session_id,
                state_id="get",
                layer="utils",
                step="get",
                agent_name="RedisClient",
                action="get",
                input_data={"key": key},
                output_data=None,
                status="fail"
            )
            raise
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Error getting key '{key}': {e}",
                session_id=session_id,
                state_id="get",
                layer="utils",
                step="get",
                agent_name="RedisClient",
                action="get",
                input_data={"key": key},
                output_data=None,
                status="fail"
            )
            return None

    def _extract_session_id_from_key(self, key: str) -> str:
        match = re.match(r"session:([^:]+):context", key)
        if match:
            return match.group(1)
        return "redis"

    @retry(
        stop=(stop_after_attempt(3) | stop_after_delay(1)),
        wait=wait_fixed(1),
        retry=retry_if_exception_type(aioredis.RedisError),
        retry_error_callback=_fallback_response
    )
    async def set(self, key: str, value: Any, ex: Optional[int] = None):
        """
        Sets a key-value pair in Redis.
        If the value is a dict or list, it's serialized to a JSON string.

        Args:
            key (str): The key to set.
            value: The value to store.
            ex (int, optional): The key's expiration time in seconds.

        Example:
            >>> # Set a dictionary that expires in 1 hour
            >>> user_data = {"name": "Alice", "permissions": ["read"]}
            >>> await redis.set("user:1", user_data, ex=3600)
        """
        session_id = self._extract_session_id_from_key(key)
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            ttl = ex if ex is not None else self.default_ttl
            await self.client.set(key, value, ex=ttl)
        except aioredis.RedisError as e:
            self.logger.log(
                level="error",
                message=f"Redis error setting key '{key}': {e}",
                session_id=session_id,
                state_id="set",
                layer="utils",
                step="set",
                agent_name="RedisClient",
                action="set",
                input_data={"key": key, "value": value, "ex": ex},
                output_data=None,
                status="fail"
            )
            raise
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Error setting key '{key}': {e}",
                session_id=session_id,
                state_id="set",
                layer="utils",
                step="set",
                agent_name="RedisClient",
                action="set",
                input_data={"key": key, "value": value, "ex": ex},
                output_data=None,
                status="fail"
            )
            raise


    @retry(
        stop=(stop_after_attempt(3) | stop_after_delay(1)),
        wait=wait_fixed(1),
        retry=retry_if_exception_type(aioredis.RedisError),
        retry_error_callback=_fallback_response
    )
    async def publish(self, channel: str, message: Any):
        """
        Publishes a message to a Redis channel.
        If the message is a dict or list, it's serialized to a JSON string.

        Args:
            channel (str): The channel to publish to.
            message: The message to send.

        Example:
            >>> # In one agent (the publisher):
            >>> await redis.publish("agent_notifications", {"agent_id": "STT", "status": "complete"})
        """
        try:
            if isinstance(message, (dict, list)):
                message = json.dumps(message)
            await self.client.publish(channel, message)
        except aioredis.RedisError as e:
            self.logger.log(
                level="error",
                message=f"Redis error publishing to channel '{channel}': {e}",
                session_id="redis",
                state_id="publish",
                layer="utils",
                step="publish",
                agent_name="RedisClient",
                action="publish",
                input_data={"channel": channel, "message": message},
                output_data=None,
                status="fail"
            )
            raise
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Error publishing to channel '{channel}': {e}",
                session_id="redis",
                state_id="publish",
                layer="utils",
                step="publish",
                agent_name="RedisClient",
                action="publish",
                input_data={"channel": channel, "message": message},
                output_data=None,
                status="fail"
            )
            raise


    @retry(
        stop=(stop_after_attempt(3) | stop_after_delay(1)),
        wait=wait_fixed(1),
        retry=retry_if_exception_type(aioredis.RedisError),
        retry_error_callback=_fallback_response
    )
    async def subscribe(self, channel: str, callback: Callable):
        """
        Subscribes to a channel and runs a callback for each message.

        This method will run indefinitely, listening for messages.

        Args:
            channel (str): The channel to subscribe to.
            callback (Callable): The async function to call with each received message.
        
        Example:
            >>> # In another agent (the subscriber):
            >>> async def notification_handler(message):
            ...     # The message is raw bytes/str, needs deserializing if JSON
            ...     data = json.loads(message)
            ...     print(f"Received notification from {data['agent_id']}: {data['status']}")
            ...
            >>> # This will run forever (or until cancelled) listening for messages.
            >>> # await redis.subscribe("agent_notifications", notification_handler)
        """
        if not self.pubsub:
            self.pubsub = self.client.pubsub()

        await self.pubsub.subscribe(channel)
        self.logger.log(
            level="info",
            message=f"Subscribed to channel '{channel}'",
            session_id="redis",
            state_id="subscribe",
            layer="utils",
            step="subscribe",
            agent_name="RedisClient",
            action="subscribe",
            input_data={"channel": channel},
            output_data=None,
            status="success"
        )
        try:
            while True:
                message = await self.pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
                if message and 'data' in message:
                    await callback(message['data'])
        except asyncio.CancelledError:
            self.logger.log(
                level="info",
                message=f"Unsubscribing from channel '{channel}'",
                session_id="redis",
                state_id="unsubscribe",
                layer="utils",
                step="unsubscribe",
                agent_name="RedisClient",
                action="unsubscribe",
                input_data={"channel": channel},
                output_data=None,
                status="success"
            )
        except aioredis.RedisError as e:
            self.logger.log(
                level="error",
                message=f"Redis error unsubscribing from channel '{channel}': {e}",
                session_id="redis",
                state_id="unsubscribe",
                layer="utils",
                step="unsubscribe",
                agent_name="RedisClient",
                action="unsubscribe",
                input_data={"channel": channel},
                output_data=None,
                status="fail"
            )
            raise
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Error in subscription for channel '{channel}': {e}",
                session_id="redis",
                state_id="subscribe",
                layer="utils",
                step="subscribe",
                agent_name="RedisClient",
                action="subscribe",
                input_data={"channel": channel},
                output_data=None,
                status="fail"
            )
            raise
        finally:
            await self.pubsub.unsubscribe(channel)

    @retry(
        stop=(stop_after_attempt(3) | stop_after_delay(1)),
        wait=wait_fixed(1),
        retry=retry_if_exception_type(aioredis.RedisError),
        retry_error_callback=_fallback_response
    )
    async def lpush(self, key: str, value: Any):
        """
        Pushes a value to the left of a Redis list (prepending it).
        Useful for creating task queues.

        Args:
            key (str): The key of the list.
            value: The value to push. It will be JSON serialized if it's a dict or list.

        Example:
            >>> await redis.lpush("task_queue", {"task_id": "123", "data": "..."})
            >>> await redis.lpush("task_queue", {"task_id": "124", "data": "..."})
        """
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            await self.client.lpush(key, value)
        except aioredis.RedisError as e:
            self.logger.log(
                level="error",
                message=f"Redis error doing lpush on key '{key}': {e}",
                session_id="redis",
                state_id="lpush",
                layer="utils",
                step="lpush",
                agent_name="RedisClient",
                action="lpush",
                input_data={"key": key, "value": value},
                output_data=None,
                status="fail"
            )
            raise
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Error doing lpush on key '{key}': {e}",
                session_id="redis",
                state_id="lpush",
                layer="utils",
                step="lpush",
                agent_name="RedisClient",
                action="lpush",
                input_data={"key": key, "value": value},
                output_data=None,
                status="fail"
            )
            raise


    @retry(
        stop=(stop_after_attempt(3) | stop_after_delay(1)),
        wait=wait_fixed(1),
        retry=retry_if_exception_type(aioredis.RedisError),
        retry_error_callback=_fallback_response
    )
    async def rpush(self, key: str, value: Any):
        """
        Pushes a value to the right of a Redis list (appending it).
        Useful for creating FIFO (First-In, First-Out) task queues.

        Args:
            key (str): The key of the list.
            value: The value to push. It will be JSON serialized if it's a dict or list.

        Example:
            >>> # Producer adds tasks
            >>> await redis.rpush("log_queue", {"event": "user_login", "user_id": 1})
            >>> await redis.rpush("log_queue", {"event": "user_logout", "user_id": 1})
            >>> # A worker using blpop would get the user_login event first.
        """
        try:
            if isinstance(value, (dict, list)):
                value = json.dumps(value)
            await self.client.rpush(key, value)
        except aioredis.RedisError as e:
            self.logger.log(
                level="error",
                message=f"Redis error doing rpush on key '{key}': {e}",
                session_id="redis",
                state_id="rpush",
                layer="utils",
                step="rpush",
                agent_name="RedisClient",
                action="rpush",
                input_data={"key": key, "value": value},
                output_data=None,
                status="fail"
            )
            raise
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Error doing rpush on key '{key}': {e}",
                session_id="redis",
                state_id="rpush",
                layer="utils",
                step="rpush",
                agent_name="RedisClient",
                action="rpush",
                input_data={"key": key, "value": value},
                output_data=None,
                status="fail"
            )
            raise

    @retry(
        stop=(stop_after_attempt(3) | stop_after_delay(1)),
        wait=wait_fixed(1),
        retry=retry_if_exception_type(aioredis.RedisError),
        retry_error_callback=_fallback_response
    )
    async def blpop(self, key: str, timeout: int = 0) -> Any:
        """
        Performs a blocking pop from the left of a Redis list.
        Useful for task queues where workers wait for jobs.

        Args:
            key (str): The key of the list to pop from.
            timeout (int): The number of seconds to block. 0 means block indefinitely.

        Returns:
            The popped value (deserialized from JSON if applicable), or None on timeout.

        Example:
            >>> # This will wait until a task appears in "task_queue" and then print it.
            >>> task = await redis.blpop("task_queue", timeout=0)
            >>> if task:
            ...     print(f"Processing task: {task['task_id']}")
        """
        try:
            result = await self.client.blpop([key], timeout=timeout)
            if result is None:
                return None
            
            value = result[1] # blpop returns a tuple (list_name, value)
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
        except aioredis.RedisError as e:
            self.logger.log(
                level="error",
                message=f"Redis error doing blpop on key '{key}': {e}",
                session_id="redis",
                state_id="blpop",
                layer="utils",
                step="blpop",
                agent_name="RedisClient",
                action="blpop",
                input_data={"key": key, "timeout": timeout},
                output_data=None,
                status="fail"
            )
            raise
        except Exception as e:
            self.logger.log(
                level="error",
                message=f"Error doing blpop on key '{key}': {e}",
                session_id="redis",
                state_id="blpop",
                layer="utils",
                step="blpop",
                agent_name="RedisClient",
                action="blpop",
                input_data={"key": key, "timeout": timeout},
                output_data=None,
                status="fail"
            )
            return None
    
    async def close(self):
        """
        Closes the Redis connection.
        """
        await self.client.close() 