import sys
from pathlib import Path
from typing import Any, Dict, Optional, List, Union
sys.path.append(str(Path(__file__).parent.parent))
import asyncio
import os
import time
import json

from dotenv import load_dotenv
from base_agent import BaseAgent
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType

class TestAgent(BaseAgent):
    def __init__(self, session_id: str = None, state_id: str = None):
        super().__init__("test_agent", session_id, state_id)
    
    async def process(self, input_data: Dict[str, Any], context: Dict[str, Any] = None) -> StateOutput:
        """
        Process input data and return a StateOutput.
        
        Args:
            input_data: Input data to process
            context: Optional context data
            
        Returns:
            StateOutput: The output state of the agent
        """
        for i in input_data:
            if type(input_data[i]) is int:
                input_data[i] = input_data[i] * 2
            elif type(input_data[i]) is str:
                input_data[i] = input_data[i].upper()
            elif type(input_data[i]) is list:
                input_data[i] = [item * 2 for item in input_data[i]]
            elif type(input_data[i]) is dict:
                input_data[i] = {key: value * 2 for key, value in input_data[i].items()}


        return StateOutput(
            status=StatusType.SUCCESS,
            message="Test agent processed input data",
            code=StatusCode.OK,
            outputs=input_data)

    def input_schema(self):
        return {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"},
                "grades": {"type": "array", "items": {"type": "number"}}
            }
        }
    
    def output_schema(self):
        return {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "number"},
                "grades": {"type": "array", "items": {"type": "number"}}
            }
        }

    def validate_input(self, input_data):
        # Simple validation based on input_schema
        schema = self.input_schema()
        if not isinstance(input_data, dict):
            return False
        for key, prop in schema.get("properties", {}).items():
            if key not in input_data:
                return False
            expected_type = prop.get("type")
            value = input_data[key]
            if expected_type == "string" and not isinstance(value, str):
                return False
            if expected_type == "number" and not isinstance(value, (int, float)):
                return False
            if expected_type == "array" and not (isinstance(value, list) and all(isinstance(i, (int, float)) for i in value)):
                return False
        return True

    def validate_output(self, output_data):
        # Simple validation based on output_schema
        schema = self.output_schema()
        if not isinstance(output_data, dict):
            return False
        for key, prop in schema.get("properties", {}).items():
            if key not in output_data:
                return False
            expected_type = prop.get("type")
            value = output_data[key]
            if expected_type == "string" and not isinstance(value, str):
                return False
            if expected_type == "number" and not isinstance(value, (int, float)):
                return False
            if expected_type == "array" and not (isinstance(value, list) and all(isinstance(i, (int, float)) for i in value)):
                return False
        return True
    def is_ready(self):
        return True

async def run_test():
    agent = TestAgent()
    input_data = {
        "name": "alice",
        "age": 25,
        "grades": [80, 90, 100]
    }
    context = {}
    result = await agent.process(input_data, context)
    print("Result:", result)
    
    await agent.start_a2a_loop()

async def a2a_test():
    # Create two agents with different names
    receiver = TestAgent(session_id="sess1", state_id="state1")
    sender = TestAgent(session_id="sess1", state_id="state2")

    # Prepare a test message using the A2AMessage schema
    test_payload = {
        "name": "bob",
        "age": 30,
        "grades": [70, 85, 90]
    }

    message = A2AMessage(
        session_id="sess1",
        message_type=MessageType.INSTRUCTION,
        source_agent="sender_agent",
        target_agent="test_agent",
        payload=test_payload,
        context_keys_updated=None
    )

    # Start the receiver's A2A loop in the background
    loop = asyncio.get_event_loop()
    receiver_task = loop.create_task(receiver.start_a2a_loop())

    # Give the receiver a moment to subscribe
    await asyncio.sleep(1)

    # Publish the message to the receiver's channel
    await sender.redis_client.publish(receiver.a2a_channel, message.to_json())

    # Optionally, subscribe to the sender's channel to receive the response
    async def on_response(msg):
        print("Received response on sender's channel:", msg)
        # You can add assertions or checks here

    await sender.redis_client.subscribe("agent_channel::sender_agent", on_response)

    # Wait a bit to allow message processing
    await asyncio.sleep(2)

    # Cancel the receiver's loop to clean up
    receiver_task.cancel()
    try:
        await receiver_task
    except asyncio.CancelledError:
        pass

if __name__ == "__main__":
    asyncio.run(run_test())
    # asyncio.run(a2a_test())
    
    