# StateOutput Schema Usage Guide

## Overview

The `StateOutput` schema provides a standardized output format for all vertical pipeline steps in the voice agent platform. This ensures consistency across all agents and makes it easier to handle responses programmatically.

## Basic Structure

Every `StateOutput` object contains these fields:

```json
{
  "status": "success",
  "message": "Human-readable description",
  "code": "200_OK",
  "outputs": { "actual_data": "here" },
  "meta": { "execution_metadata": "here" }
}
```

## Quick Start

### Import the Schema

```python
from schemas.outputSchema import (
    StateOutput, StatusType, StatusCode,
    success_output, error_output, warning_output
)
```

### Create a Success Response

```python
# Using the factory function (recommended)
result = success_output(
    message="Audio processed successfully",
    outputs={
        "transcript": "Hello world",
        "confidence": 0.95
    },
    meta={
        "agent_name": "audio_agent",
        "processing_time_ms": 150
    }
)

# Using the class directly
result = StateOutput(
    status=StatusType.SUCCESS,
    message="Audio processed successfully",
    code=StatusCode.OK,
    outputs={"transcript": "Hello world"},
    meta={"agent_name": "audio_agent"}
)
```

### Create an Error Response

```python
result = error_output(
    message="Audio quality too poor for transcription",
    code=StatusCode.AUDIO_ERROR,
    meta={
        "agent_name": "audio_agent",
        "noise_level": 0.85
    }
)
```

## Status Types

- `SUCCESS`: Operation completed successfully
- `ERROR`: Operation failed with an error
- `WARNING`: Operation completed but with warnings
- `PENDING`: Operation is queued but not started
- `PROCESSING`: Operation is currently running
- `TIMEOUT`: Operation exceeded time limit
- `CANCELLED`: Operation was cancelled

## Status Codes

### Success Codes (2xx)
- `200_OK`: Standard success
- `201_CREATED`: Resource created
- `202_ACCEPTED`: Request accepted for processing
- `204_NO_CONTENT`: Success but no content to return

### Client Error Codes (4xx)
- `400_BAD_REQUEST`: Invalid request parameters
- `401_UNAUTHORIZED`: Authentication required
- `403_FORBIDDEN`: Access denied
- `404_NOT_FOUND`: Resource not found
- `408_TIMEOUT`: Request timeout
- `422_VALIDATION_ERROR`: Input validation failed

### Server Error Codes (5xx)
- `500_INTERNAL_ERROR`: General server error
- `503_SERVICE_UNAVAILABLE`: Service temporarily unavailable
- `504_GATEWAY_TIMEOUT`: Gateway timeout

### Agent-Specific Codes (6xx)
- `600_AGENT_ERROR`: General agent error
- `601_PIPELINE_ERROR`: Pipeline execution error
- `602_MEMORY_ERROR`: Memory/session error
- `603_AUDIO_ERROR`: Audio processing error
- `604_LLM_ERROR`: Language model error
- `605_RAG_ERROR`: RAG/knowledge base error

## Agent Integration Examples

### Audio Agent

```python
def process_audio(self, audio_data):
    try:
        transcript = self.transcribe(audio_data)
        return success_output(
            message="Audio transcribed successfully",
            outputs={
                "transcript": transcript,
                "confidence": 0.95,
                "language": "en-US"
            },
            meta={
                "agent_name": "audio_agent",
                "model_used": "whisper-large-v3"
            }
        )
    except AudioQualityError:
        return error_output(
            message="Audio quality insufficient for transcription",
            code=StatusCode.AUDIO_ERROR,
            meta={"agent_name": "audio_agent"}
        )
```

### Response Agent

```python
def generate_response(self, user_input):
    try:
        response = self.llm.generate(user_input)
        return success_output(
            message="Response generated successfully",
            outputs={
                "response_text": response,
                "intent": "question_answer",
                "confidence": 0.89
            },
            meta={
                "agent_name": "response_agent",
                "model_used": "gpt-4",
                "tokens_used": 45
            }
        )
    except Exception as e:
        return error_output(
            message=f"Failed to generate response: {str(e)}",
            code=StatusCode.LLM_ERROR,
            meta={"agent_name": "response_agent"}
        )
```

## Pipeline Integration

### Using in Pipeline Steps

```python
def pipeline_step(self, input_data):
    # Process the input
    result = self.process(input_data)
    
    # Return standardized output
    return PipelineStateOutput(
        status=StatusType.SUCCESS,
        message="Step completed successfully",
        code=StatusCode.OK,
        outputs={"processed_data": result},
        meta={
            "step_name": "audio_processing",
            "step_index": 1,
            "pipeline_id": "pipe_123"
        }
    )
```

### Handling Pipeline Responses

```python
def handle_pipeline_response(self, response: StateOutput):
    if response.is_success():
        # Process successful response
        data = response.get_output("processed_data")
        return self.next_step(data)
    
    elif response.is_error():
        # Handle error
        error_msg = response.message
        self.log_error(error_msg)
        return self.handle_error(response)
    
    else:
        # Handle other statuses (warning, processing, etc.)
        return self.handle_status(response)
```

## Validation and Best Practices

### Automatic Validation

The schema automatically validates:
- Message length (1-1000 characters)
- Required fields are present
- Data types are correct
- No extra fields are allowed

### Best Practices

1. **Use factory functions** for common patterns:
   ```python
   # Good
   return success_output("Operation completed", outputs=data)
   
   # Less preferred
   return StateOutput(status=StatusType.SUCCESS, ...)
   ```

2. **Include relevant metadata**:
   ```python
   meta={
       "agent_name": "audio_agent",
       "processing_time_ms": 150,
       "model_used": "whisper-large-v3",
       "session_id": session_id
   }
   ```

3. **Use appropriate status codes**:
   ```python
   # For validation errors
   code=StatusCode.VALIDATION_ERROR
   
   # For agent-specific errors
   code=StatusCode.AUDIO_ERROR
   ```

4. **Provide meaningful messages**:
   ```python
   # Good
   message="Audio transcribed successfully with 95% confidence"
   
   # Poor
   message="Done"
   ```

## Testing

Run the test suite to validate your StateOutput usage:

```bash
python -m pytest tests/test_state_output_schema.py -v
```

## Common Patterns

### Long-Running Operations

```python
# Initial response
return StateOutput(
    status=StatusType.PROCESSING,
    message="Large file processing in progress",
    code=StatusCode.ACCEPTED,
    outputs={"progress_percent": 25},
    meta={"estimated_completion_ms": 5000}
)
```

### Warnings with Partial Success

```python
return warning_output(
    message="Processing completed but with low confidence",
    outputs={"result": data, "confidence": 0.45},
    meta={"threshold": 0.7, "actual": 0.45}
)
```

### Chaining Operations

```python
def process_chain(self, input_data):
    # Step 1
    step1_result = self.step1(input_data)
    if not step1_result.is_success():
        return step1_result  # Propagate error
    
    # Step 2
    step2_result = self.step2(step1_result.outputs)
    if not step2_result.is_success():
        return step2_result  # Propagate error
    
    # Final success
    return success_output(
        message="All steps completed successfully",
        outputs=step2_result.outputs
    )
```
