{"id": "l2_greeting", "version": "1.0", "pipeline": [{"step": "SpeechToText", "agent": "stt_google", "input": {"audio": "user_input_audio"}, "tools": {"external_tools": "stt"}, "output": {"text": "transcribed_text"}}, {"step": "IntentDetection", "agent": "intent_classifier", "input": {"text": "transcribed_text"}, "tools": {"external_tools": "openai"}, "output": {"intent": "intent", "slots": "slots"}}, {"step": "LLM_Acknowledgment", "agent": "llm_ack_gen", "input": {"intent": "intent"}, "tools": {"external_tools": "openai"}, "output": {"acknowledgment": "ack_text"}}, {"step": "TTS_Response", "agent": "tts_google", "input": {"text": "ack_text"}, "tools": {"external_tools": "openai"}, "output": {"audio": "audio_output", "acknowledgment": "acknowledgment", "transition_signal": "transition_signal"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "IntentDetection"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}